import mongoose, { Document, Schema, Model } from 'mongoose';

// --- Interfaces ---

interface Material {
  _id?: mongoose.Types.ObjectId;
  name: string;
  description: string;
  cost: number;
  quantity: number;
}

interface DeliveryMaterial {
  materialId: mongoose.Types.ObjectId;
  quantity: number;
}

interface DeliveryLocation {
  name: string;
  address: string;
  materials: {
    _id?: mongoose.Types.ObjectId;
    name: string;
    description: string;
    cost: number;
    quantity: number;
  }[];
}

interface Load {
  deliveryLocation: {
    street?: string;
    city?: string;
    state?: string;
    postalCode?: string;
    latitude?: number;
    longitude?: number;
  };
  materials: {
    materialId: number;
    materialName: string;
    quantity: number;
    loadType: 'tons' | 'cubic_yards';
  }[];
}

interface Transaction {
  _id?: mongoose.Types.ObjectId;
  truckerId: mongoose.Types.ObjectId;
  status: 'Reserved' | 'PickedUp' | 'Completed';
  reservedAt: Date;
  pickedUpAt?: Date;
  completedAt?: Date;
  reservedMaterials: {
    materialId: string | number;
    materialName: string;
    quantity: number;
    deliveryLocationName: string;
  }[];
  signature?: string;
  pickupTicketImage?: string;
  materialsPickedUp?: {
    materialId: string | number;
    quantity: number;
    deliveryLocationName: string;
  }[];
  deliverySignature?: string;
  deliveryTicketImage?: string;
  materialsDelivered?: {
    materialId: string | number;
    quantity: number;
    deliveryLocationName: string;
  }[];
}

export interface IPurchaseOrder {
  clientName: string;
  orderNumber: number;
  pickupLocation: {
    name: string;
    address: string;
    coordinates: {
      lat: number;
      lng: number;
    };
  };
  materials: {
    id: number;
    name: string;
    quantity: number;
    loadType: 'tons' | 'cubic_yards';
  }[];
  loads: Load[];
  transactions: Transaction[];
  totalDeliveredQuantities: {
    materialId: string | number;
    deliveryLocation: string;
    quantity: number;
  }[];
  isCompleted: boolean;
  createdAt: Date;
}
// --- Schemas ---

const MaterialSchema = new Schema<Material>({
  name: { type: String, required: true },
  description: { type: String, required: true },
  cost: { type: Number, required: true },
  quantity: { type: Number, required: true },
}, { _id: true });

const DeliveryMaterialSchema = new Schema<DeliveryMaterial>({
  materialId: { type: Schema.Types.ObjectId, ref: 'Material', required: true },
  quantity: { type: Number, required: true },
}, { _id: false });

const DeliveryLocationSchema = new Schema<DeliveryLocation>({
  name: { type: String, required: true },
  address: { type: String, required: true },
  materials: { type: [MaterialSchema], required: true },
}, { _id: false });

const TransactionSchema = new Schema<Transaction>({
  truckerId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  status: { type: String, enum: ['Reserved', 'PickedUp', 'Completed'], required: true },
  reservedAt: { type: Date, default: Date.now },
  pickedUpAt: { type: Date },
  completedAt: { type: Date },
  reservedMaterials: [{
    materialId: { type: Schema.Types.Mixed, required: true },
    materialName: { type: String, required: true },
    quantity: { type: Number, required: true },
    deliveryLocationName: { type: String, required: true },
  }],
  signature: { type: String },
  pickupTicketImage: { type: String },
  materialsPickedUp: [{
    materialId: { type: Schema.Types.Mixed, required: true },
    quantity: { type: Number, required: true },
    deliveryLocationName: { type: String, required: true },
  }],
  deliverySignature: { type: String },
  deliveryTicketImage: { type: String },
  materialsDelivered: [{
    materialId: { type: Schema.Types.Mixed, required: true },
    quantity: { type: Number, required: true },
    deliveryLocationName: { type: String, required: true },
  }]
}, { _id: true });

const LoadSchema = new Schema<Load>({
  deliveryLocation: {
    street: { type: String, required: false },
    city: { type: String, required: false },
    state: { type: String, required: false },
    postalCode: { type: String, required: false },
    latitude: { type: Number, required: false },
    longitude: { type: Number, required: false }
  },
  materials: [{
    materialId: { type: Number, required: true },
    materialName: { type: String, required: true },
    quantity: { type: Number, required: true },
    loadType: { type: String, enum: ['tons', 'cubic_yards'], required: true },
  }]
}, { _id: false });

const PickupMaterialSchema = new Schema({
  id: { type: Number, required: true },
  name: { type: String, required: true },
  quantity: { type: Number, required: true },
  loadType: { type: String, enum: ['tons', 'cubic_yards'], required: true },
}, { _id: false });

const PurchaseOrderSchema = new Schema<IPurchaseOrder>({
  clientName: { type: String, required: true },
  orderNumber: { type: Number, required: true, unique: true },
  pickupLocation: {
    name: { type: String, required: true },
    address: { type: String, required: true },
    coordinates: {
      lat: { type: Number, required: true },
      lng: { type: Number, required: true },
    }
  },
  materials: { type: [PickupMaterialSchema], default: [] },
  loads: { type: [LoadSchema], required: true },
  transactions: { type: [TransactionSchema], default: [] },
  totalDeliveredQuantities: [{
    materialId: { type: Schema.Types.Mixed, required: true },
    deliveryLocation: { type: String, required: true },
    quantity: { type: Number, required: true },
  }],
  isCompleted: { type: Boolean, default: false },
  createdAt: { type: Date, default: Date.now },
});

// --- Model ---

export const PurchaseOrder =
  mongoose.models.PurchaseOrder as mongoose.Model<IPurchaseOrder> ||
  mongoose.model<IPurchaseOrder>('PurchaseOrder', PurchaseOrderSchema);
