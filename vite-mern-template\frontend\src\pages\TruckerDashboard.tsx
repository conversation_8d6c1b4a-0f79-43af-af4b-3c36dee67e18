import React, { useEffect, useState, useRef } from "react";
import axios from "axios";
import SignaturePad from 'react-signature-canvas';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import LanguageSwitcher from '../components/LanguageSwitcher';

interface Load {
  deliveryLocation: string;
  materials: {
    materialId: number;
    materialName: string;
    quantity: number;
    loadType: 'tons' | 'cubic_yards';
    remainingQuantity?: number;
    deliveredQuantity?: number;
  }[];
}

interface Material {
  id: number;
  _id?: string;
  name: string;
  description: string;
  cost: number;
  quantity: number;
  loadType?: 'tons' | 'cubic_yards';
  remainingQuantity?: number;
  deliveredQuantity?: number;
}

interface PickupLocation {
  name: string;
  address: string;
  coordinates: {
    lat: number;
    lng: number;
  };
}

interface ReservedMaterial {
  materialId: string; // ← now matches MongoDB _id
  materialName: string;
  quantity: number;
}

interface ReservedTransaction {
  purchaseOrderId: string;
  deliveryLocationName: string;
  reservedMaterials: ReservedMaterial[];
  transactionId: string;
  pickupCompleted?: boolean;
}

interface PurchaseOrder {
  _id: string;
  orderNumber: number;
  clientName: string;
  pickupLocation: PickupLocation;
  loads: Load[];
  createdAt: string;
  isCompleted?: boolean;
  materials: Material[];
}

const BottomNavLink: React.FC<{
  icon: string;
  label: string;
  active?: boolean;
}> = ({ icon, label, active }) => {
  const baseClass =
    "flex flex-col items-center justify-center gap-1 w-1/3 text-xs font-medium tracking-wide";
  const activeClass = active
    ? "text-[var(--primary-color)]"
    : "text-[var(--text-secondary)] hover:text-[var(--primary-color)]";

  return (
    <a className={`${baseClass} ${activeClass}`} href="#">
      <span className={`material-icons${active ? "" : "-outlined"} icon-size`}>{icon}</span>
      <p>{label}</p>
    </a>
  );
};

const Dashboard: React.FC = () => {
  const { t } = useTranslation();
  const [orders, setOrders] = useState<PurchaseOrder[]>([]);
  const [reservedTransaction, setReservedTransaction] = useState<ReservedTransaction | null>(null);
  const [pickupCompleted, setPickupCompleted] = useState(false);
  const [pickupQuantities, setPickupQuantities] = useState<{ materialId: string; quantity: number }[]>([]);
  const [pickupTicket, setPickupTicket] = useState<File | null>(null);
  const [completeModalOpen, setCompleteModalOpen] = useState(false);
  const [deliveryModalOpen, setDeliveryModalOpen] = useState(false);
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [viewSignatureModal, setViewSignatureModal] = useState(false);
  const [viewingSignature, setViewingSignature] = useState<string>('');
  const [viewingType, setViewingType] = useState<'pickup' | 'delivery'>('pickup');
  const [viewTicketModal, setViewTicketModal] = useState(false);
  const [viewingTicket, setViewingTicket] = useState<string>('');
  const [completedTransactions, setCompletedTransactions] = useState<any[]>([]);
  const [showCompletedTransactions, setShowCompletedTransactions] = useState(false);
  const [developerMode, setDeveloperMode] = useState(false);
  const [user, setUser] = useState<any>(null);
  const [licensePhoto, setLicensePhoto] = useState<File | null>(null);
  const [insurancePhoto, setInsurancePhoto] = useState<File | null>(null);
  const [uploading, setUploading] = useState(false);
  const [uploadMsg, setUploadMsg] = useState<string | null>(null);
  const [showDocsModal, setShowDocsModal] = useState(false);
  const navigate = useNavigate();

  const signaturePadRef = useRef<SignaturePad>(null);
  const deliverySignaturePadRef = useRef<SignaturePad>(null);

  useEffect(() => {
    const token = localStorage.getItem('token');
    if (!token) {
      navigate('/');
    }
    fetchOrders();
    fetchCompletedTransactions();
    fetchActiveReservation();
    fetchUser();
  }, []);

  const fetchOrders = async () => {
    try {
      const token = localStorage.getItem("token");
      const headers = token ? { Authorization: `Bearer ${token}` } : {};
      const res = await axios.get<PurchaseOrder[]>("/api/purchase-orders", { headers });
      setOrders(res.data);
    } catch (err) {
      console.error("Failed to fetch POs", err);
    }
  };

  const fetchCompletedTransactions = async () => {
    try {
      const token = localStorage.getItem("token");
      const headers = token ? { Authorization: `Bearer ${token}` } : {};
      const res = await axios.get("/api/purchase-orders/transactions", { headers });
      setCompletedTransactions(res.data);
    } catch (err) {
      console.error("Failed to fetch completed transactions", err);
    }
  };

  const fetchActiveReservation = async () => {
    try {
      const token = localStorage.getItem("token");
      const headers = token ? { Authorization: `Bearer ${token}` } : {};
      const res = await axios.get("/api/purchase-orders/active-reservations", { headers });
      if (res.data && res.data.length > 0) {
        // Only support one active reservation at a time for this UI
        const tx = res.data[0];
        setReservedTransaction({
          purchaseOrderId: tx.purchaseOrderId,
          deliveryLocationName: tx.deliveryLocation,
          reservedMaterials: tx.reservedMaterials,
          transactionId: tx.transactionId,
          pickupCompleted: tx.pickupCompleted,
        });
        // Use the pickupCompleted field from backend
        setPickupCompleted(tx.pickupCompleted);
      } else {
        setReservedTransaction(null);
        setPickupCompleted(false);
      }
    } catch (err) {
      console.error("Failed to fetch active reservation", err);
      setReservedTransaction(null);
      setPickupCompleted(false);
    }
  };

  const fetchUser = async () => {
    try {
      const token = localStorage.getItem('token');
      const headers = token ? { Authorization: `Bearer ${token}` } : {};
      // Try to get user id from token payload
      let userId = null;
      if (token) {
        try {
          const payload = JSON.parse(atob(token.split('.')[1]));
          userId = payload.id;
        } catch (e) {}
      }
      if (!userId) return;
      const res = await axios.get(`/api/auth/users/${userId}`, { headers });
      setUser(res.data);
    } catch (err) {
      // ignore
    }
  };

  const handleUpload = async (type: 'license' | 'insurance') => {
    if (!user) return;
    setUploading(true);
    setUploadMsg(null);
    const formData = new FormData();
    if (type === 'license' && licensePhoto) formData.append('license', licensePhoto);
    if (type === 'insurance' && insurancePhoto) formData.append('insurance', insurancePhoto);
    try {
      const token = localStorage.getItem('token');
      const headers = token ? { Authorization: `Bearer ${token}` } : {};
      await axios.post(`/api/auth/users/${user._id}/${type}`, formData, { headers });
      setUploadMsg('Upload successful!');
      // Refresh user info
      const res = await axios.get(`/api/auth/users/${user._id}`, { headers });
      setUser(res.data);
    } catch (err) {
      setUploadMsg('Upload failed.');
    }
    setUploading(false);
  };

  const viewSignature = async (type: 'pickup' | 'delivery', purchaseOrderId?: string, transactionId?: string) => {
    const poId = purchaseOrderId || reservedTransaction?.purchaseOrderId;
    const txId = transactionId || reservedTransaction?.transactionId;

    if (!poId || !txId) return;

    try {
      const token = localStorage.getItem("token");
      const headers = token ? { Authorization: `Bearer ${token}` } : {};

      const res = await axios.get(
        `/api/purchase-orders/${poId}/transactions/${txId}/signature/${type}`,
        { headers }
      );

      setViewingSignature(res.data.signature);
      setViewingType(type);
      setViewSignatureModal(true);
    } catch (error) {
      console.error('Failed to fetch signature:', error);
      alert('Failed to fetch signature');
    }
  };

  const viewTicket = async (type: 'pickup' | 'delivery', purchaseOrderId?: string, transactionId?: string) => {
    const poId = purchaseOrderId || reservedTransaction?.purchaseOrderId;
    const txId = transactionId || reservedTransaction?.transactionId;

    if (!poId || !txId) return;

    try {
      const token = localStorage.getItem("token");
      const headers = token ? { Authorization: `Bearer ${token}` } : {};

      const res = await axios.get(
        `/api/purchase-orders/${poId}/transactions/${txId}/ticket/${type}`,
        { headers }
      );

      setViewingTicket(res.data.ticketImage);
      setViewingType(type);
      setViewTicketModal(true);
    } catch (error) {
      console.error('Failed to fetch ticket:', error);
      alert('Failed to fetch ticket');
    }
  };

  // Helper function to calculate distance between two coordinates (Haversine formula)
  function getDistanceFromLatLonInKm(lat1: number, lon1: number, lat2: number, lon2: number) {
    const R = 6371; // Radius of the earth in km
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
      Math.sin(dLon / 2) * Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const d = R * c; // Distance in km
    return d;
  }

  // Filter out completed orders for trucker
  const availableOrders = orders.filter(order => !order.isCompleted);

  // Geofencing check for pickup and delivery
  async function checkGeofence(targetLat: number, targetLng: number): Promise<boolean> {
    if (developerMode) return true;
    return new Promise((resolve) => {
      if (!navigator.geolocation) {
        alert('Geolocation is not supported by your browser.');
        resolve(false);
        return;
      }
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords;
          const distance = getDistanceFromLatLonInKm(latitude, longitude, targetLat, targetLng);
          if (distance <= 2) {
            resolve(true);
          } else {
            alert(`You must be within 2km of the location to perform this action. Current distance: ${distance.toFixed(2)} km`);
            resolve(false);
          }
        },
        (error) => {
          alert('Unable to retrieve your location.');
          resolve(false);
        }
      );
    });
  }

  return (
    <div className="relative flex min-h-screen flex-col overflow-x-hidden bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      {/* Header */}
      <header className="sticky top-0 z-10 bg-white/90 backdrop-blur-sm shadow-lg border-b border-gray-200">
        <div className="flex items-center justify-between p-4 pb-3">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-md">
              <span className="text-white text-lg">🚛</span>
            </div>
            <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
              {t('dashboard.dashboard')}
            </h1>
          </div>
          <div className="flex items-center gap-3">
            <button
              className="px-4 py-2 rounded-xl bg-gradient-to-r from-gray-100 to-gray-200 text-blue-700 border border-blue-200 hover:from-blue-100 hover:to-blue-200 text-sm font-semibold shadow-md transition-all duration-200 transform hover:scale-105"
              onClick={() => setShowDocsModal(true)}
            >
              📄 Driver Documents
            </button>
            <LanguageSwitcher />
            <button
              className={`px-4 py-2 rounded-xl font-semibold shadow-md transition-all duration-200 transform hover:scale-105 ${
                developerMode
                  ? 'bg-gradient-to-r from-green-500 to-green-600 text-white hover:from-green-600 hover:to-green-700'
                  : 'bg-gradient-to-r from-gray-200 to-gray-300 text-gray-800 hover:from-gray-300 hover:to-gray-400'
              }`}
              onClick={() => setDeveloperMode((d) => !d)}
            >
              {developerMode ? '🔧 Dev Mode: ON' : '🔧 Dev Mode: OFF'}
            </button>
            <button
              className="px-4 py-2 rounded-xl bg-gradient-to-r from-red-500 to-red-600 text-white font-semibold shadow-md hover:from-red-600 hover:to-red-700 transition-all duration-200 transform hover:scale-105"
              onClick={() => {
                localStorage.clear();
                navigate('/');
              }}
            >
              🚪 {t('dashboard.logout')}
            </button>
          </div>
        </div>
      </header>

      {/* Main content */}
      <main className="flex-grow overflow-y-auto pb-24 px-4">
        {/* Summary */}
        <section className="py-6">
          <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
            <h2 className="text-xl font-bold text-gray-800 mb-6 flex items-center gap-2">
              📊 {t('dashboard.summary')}
            </h2>
            <div className="grid grid-cols-2 gap-6">
              <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl shadow-md p-6 border border-blue-200 hover:shadow-lg transition-all duration-200 transform hover:scale-105">
                <div className="flex items-center justify-between mb-3">
                  <div className="text-3xl">📦</div>
                  <div className="text-2xl font-bold text-blue-700">{orders.length}</div>
                </div>
                <div className="text-blue-800 font-semibold">{t('dashboard.totalOrders')}</div>
              </div>

              <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-2xl shadow-md p-6 border border-green-200 hover:shadow-lg transition-all duration-200 transform hover:scale-105">
                <div className="flex items-center justify-between mb-3">
                  <div className="text-3xl">✅</div>
                  <div className="text-2xl font-bold text-green-700">{completedTransactions.length}</div>
                </div>
                <div className="text-green-800 font-semibold">{t('dashboard.completedOrders')}</div>
              </div>
            </div>
          </div>
        </section>

        {/* Available Orders / Reserved Transaction */}
        <section className="py-6">
          <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
            <h3 className="text-xl font-bold text-gray-800 mb-6 flex items-center gap-2">
              🚛 {t('dashboard.availableOrders')}
            </h3>

            {reservedTransaction ? (
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-2xl border border-blue-200 shadow-md mb-6">
              <p className="text-base font-medium text-[var(--text-primary)]">
                Reserved Pickup for Order #{reservedTransaction.purchaseOrderId.slice(-5).toUpperCase()}
              </p>
              <p className="text-sm text-[var(--text-secondary)] mb-1">
                Delivery Location: {reservedTransaction.deliveryLocationName}
              </p>
              <p className="text-sm text-[var(--text-secondary)] mb-1">Reserved Materials:</p>
              <ul className="list-disc list-inside text-xs text-[var(--text-secondary)] mb-2">
                {reservedTransaction.reservedMaterials.map((m) => {
                  // Find the original material in the order to get the loadType
                  const order = orders.find(o => o._id === reservedTransaction.purchaseOrderId);
                  const load = order?.loads.find(l => l.deliveryLocation === reservedTransaction.deliveryLocationName);
                  const originalMaterial = load?.materials.find(mat => mat.materialId.toString() === m.materialId);

                  return (
                    <li key={m.materialId + '-' + m.materialName} className="mb-1">
                      <span className="font-medium">{m.materialName}</span>
                      <div className="pl-4">
                        <p>Reserved Quantity: {m.quantity} {originalMaterial?.loadType || ''}</p>
                        {originalMaterial?.remainingQuantity !== undefined && (
                          <p className="text-blue-600">
                            Available: {originalMaterial.remainingQuantity} {originalMaterial.loadType}
                            {originalMaterial.deliveredQuantity ? ` (${originalMaterial.deliveredQuantity} delivered)` : ''}
                          </p>
                        )}
                      </div>
                    </li>
                  );
                })}
              </ul>
              {pickupCompleted ? (
                <div className="space-y-2">
                  <div className="bg-green-100 border border-green-300 text-green-800 px-3 py-2 rounded text-sm">
                    ✓ Pickup completed successfully
                  </div>
                  <button
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 rounded-lg text-sm transition-colors"
                    onClick={() => setDeliveryModalOpen(true)}
                  >
                    Proceed to Delivery
                  </button>
                </div>
              ) : (
                <div className="space-y-2">
                  <button
                    className="w-full bg-green-600 hover:bg-green-700 text-white py-2 rounded-lg text-sm transition-colors"
                    onClick={() => setCompleteModalOpen(true)}
                  >
                    Complete Pickup
                  </button>
                  <button
                    className="w-full bg-red-100 hover:bg-red-200 text-red-700 py-2 rounded-lg text-sm transition-colors"
                    onClick={() => setShowCancelModal(true)}
                  >
                    Cancel Reservation
                  </button>
                </div>
              )}
            </div>
          ) : (
            <div className="grid gap-6 md:grid-cols-2">
              {availableOrders.length === 0 ? (
                <div className="text-center text-gray-500 text-lg py-8">{t('dashboard.noAvailableOrders')}</div>
          ) : (
            availableOrders.map((order) => (
              <OrderCard
                key={order._id}
                id={order._id}
                client={order.clientName}
                pickupLocation={`${order.pickupLocation.name} (${order.pickupLocation.address})`}
                loads={order.loads}
                isCompleted={order.isCompleted}
                t={t}
              />
            ))
              )}
            </div>
          )}
        </section>

        {/* Transaction History */}
        <section className="p-4">
          <div className="flex justify-between items-center mb-3">
            <h2 className="text-lg font-semibold text-[var(--text-primary)]">{t('dashboard.transactionHistory')}</h2>
            <button
              className="text-[var(--primary-color)] text-sm font-medium flex items-center gap-1"
              onClick={() => setShowCompletedTransactions(!showCompletedTransactions)}
            >
              {showCompletedTransactions ? t('dashboard.hideCompleted') : t('dashboard.showCompleted')}
              <span className="material-icons text-base">arrow_forward_ios</span>
            </button>
          </div>

          <div className="space-y-3">
            {completedTransactions.length === 0 ? (
              <p className="text-sm text-gray-500">{t('dashboard.noCompletedTransactions')}</p>
            ) : (
              completedTransactions.map((tx) => (
                <HistoryCard
                  key={`${tx.purchaseOrderId}-${tx.transactionId}`}
                  number={`#${tx.orderNumber}`}
                  customer={tx.clientName}
                  amount={tx.reservedMaterials && tx.reservedMaterials.length > 0 ? `$${tx.reservedMaterials.reduce((sum: number, m: any) => sum + (m.cost || 0), 0)}` : '$0'}
                  status={t('dashboard.completed')}
                  purchaseOrderId={tx.purchaseOrderId}
                  transactionId={tx.transactionId}
                  deliveredMaterials={tx.deliveredMaterials || tx.reservedMaterials}
                  onViewSignature={(type) => viewSignature(type, tx.purchaseOrderId, tx.transactionId)}
                  onViewTicket={(type) => viewTicket(type, tx.purchaseOrderId, tx.transactionId)}
                  t={t}
                />
              ))
            )}
          </div>
        </section>
      </main>

      {/* Reserve Pickup Modal */}
      {completeModalOpen && reservedTransaction && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4">
          <div className="bg-white p-8 rounded-2xl shadow-2xl w-full max-w-lg max-h-[90vh] overflow-auto border border-gray-200">
            <div className="flex items-center gap-3 mb-6">
              <div className="text-3xl">📦</div>
              <h3 className="text-2xl font-bold text-gray-800">
                Complete Pickup
              </h3>
            </div>

            <p className="mb-2 text-sm font-medium">Enter picked-up quantities:</p>
            {reservedTransaction.reservedMaterials.map(mat => (
              <div key={mat.materialId} className="mb-2">
                <label className="block text-sm text-gray-700">{mat.materialName}</label>
                <label className="block text-xs text-gray-500 mt-1 mb-0.5">Quantity</label>
                <input
                  type="number"
                  min={1}
                  value={pickupQuantities.find(q => q.materialId === mat.materialId)?.quantity || ''}
                  onChange={(e) => {
                    const quantity = Number(e.target.value);
                    setPickupQuantities(prev => {
                      const exists = prev.find(q => q.materialId === mat.materialId);
                      if (exists) {
                        return prev.map(q => q.materialId === mat.materialId ? { materialId: mat.materialId, quantity } : q);
                      } else {
                        return [...prev, { materialId: mat.materialId, quantity }];
                      }
                    });
                  }}
                  className="w-full border border-gray-300 rounded p-1 mt-1"
                />
              </div>
            ))}

            <div className="mb-3">
              <label className="block text-sm font-medium mb-1">Draw Signature:</label>
              <div className="flex justify-center">
                <div className="border border-gray-300 rounded bg-gray-100 mb-2" style={{ width: 320, height: 120, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                  {/* @ts-ignore */}
                  <SignaturePad
                    ref={signaturePadRef}
                    penColor="black"
                    canvasProps={{ width: 300, height: 100, style: { display: 'block', margin: '0 auto', background: '#f3f4f6' } }}
                    backgroundColor="#f3f4f6"
                  />
                </div>
              </div>
              <button
                type="button"
                className="text-xs text-blue-600 underline mb-2"
                onClick={() => signaturePadRef.current?.clear()}
              >
                Clear Signature
              </button>
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium mb-1">Upload Pickup Ticket:</label>
              <input
                type="file"
                accept="image/*"
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  if (file) {
                    setPickupTicket(file);
                  }
                }}
              />
            </div>

            <div className="flex justify-end gap-3">
              <button
                className="px-4 py-2 rounded-md bg-gray-200 text-gray-800 font-medium hover:bg-gray-300"
                onClick={() => setCompleteModalOpen(false)}
              >
                Cancel
              </button>
              <button
                className="px-4 py-2 rounded-md bg-green-600 text-white font-medium hover:bg-green-700"
                onClick={async () => {
                  // Geofencing for pickup: must be within 2km of pickup location
                  if (!reservedTransaction) return;
                  const order = orders.find(o => o._id === reservedTransaction.purchaseOrderId);
                  if (!order) return alert('Order not found.');
                  const { lat, lng } = order.pickupLocation.coordinates;
                  const withinGeofence = await checkGeofence(lat, lng);
                  if (!withinGeofence) return;
                  const token = localStorage.getItem("token");
                  const headers = token ? { Authorization: `Bearer ${token}` } : {};

                  const formData = new FormData();
                  const sigData = signaturePadRef.current && !signaturePadRef.current.isEmpty() ? signaturePadRef.current.getCanvas().toDataURL('image/png') : '';
                  formData.append('signature', sigData);
                  formData.append('materialsData', JSON.stringify(pickupQuantities.map(q => {
                    const locationName = reservedTransaction.deliveryLocationName;
                    return { ...q, deliveryLocationName: locationName };
                  })));
                  formData.append('action', 'pickup');
                  if (pickupTicket) {
                    formData.append('ticket', pickupTicket);
                  }

                  try {
                    await axios.post(
                      `/api/purchase-orders/${reservedTransaction.purchaseOrderId}/complete/${reservedTransaction.transactionId}`,
                      formData,
                      { headers }
                    );
                    setCompleteModalOpen(false);
                    setPickupCompleted(true);
                    setPickupQuantities([]);
                    setPickupTicket(null);
                    // Refresh the active reservation to get updated status
                    fetchActiveReservation();
                  } catch (error) {
                    console.error('Failed to complete pickup:', error);
                    alert('Failed to complete pickup');
                  }
                }}
              >
                Submit
              </button>
            </div>
          </div>
        </div>
      )}
      {deliveryModalOpen && reservedTransaction && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white p-6 rounded-xl shadow-lg w-[90%] max-w-md max-h-[90vh] overflow-auto">
            <h3 className="text-xl font-semibold mb-4 text-[var(--text-primary)]">
              Complete Delivery
            </h3>

            <p className="mb-2 text-sm font-medium">Enter delivered quantities:</p>
            {reservedTransaction.reservedMaterials.map(mat => (
              <div key={mat.materialId} className="mb-2">
                <label className="block text-sm text-gray-700">{mat.materialName}</label>
                <label className="block text-xs text-gray-500 mt-1 mb-0.5">Quantity</label>
                <input
                  type="number"
                  min={1}
                  value={pickupQuantities.find(q => q.materialId === mat.materialId)?.quantity || ''}
                  onChange={(e) => {
                    const quantity = Number(e.target.value);
                    setPickupQuantities(prev => {
                      const exists = prev.find(q => q.materialId === mat.materialId);
                      if (exists) {
                        return prev.map(q => q.materialId === mat.materialId ? { materialId: mat.materialId, quantity } : q);
                      } else {
                        return [...prev, { materialId: mat.materialId, quantity }];
                      }
                    });
                  }}
                  className="w-full border border-gray-300 rounded p-1 mt-1"
                />
              </div>
            ))}

            <div className="mb-3">
              <label className="block text-sm font-medium mb-1">Draw Signature:</label>
              <div className="flex justify-center">
                <div className="border border-gray-300 rounded bg-gray-100 mb-2" style={{ width: 320, height: 120, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                  {/* @ts-ignore */}
                  <SignaturePad
                    ref={deliverySignaturePadRef}
                    penColor="black"
                    canvasProps={{ width: 300, height: 100, style: { display: 'block', margin: '0 auto', background: '#f3f4f6' } }}
                    backgroundColor="#f3f4f6"
                  />
                </div>
              </div>
              <button
                type="button"
                className="text-xs text-blue-600 underline mb-2"
                onClick={() => deliverySignaturePadRef.current?.clear()}
              >
                Clear Signature
              </button>
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium mb-1">Upload Delivery Ticket:</label>
              <input
                type="file"
                accept="image/*"
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  if (file) {
                    setPickupTicket(file);
                  }
                }}
              />
            </div>

            <div className="flex justify-end gap-3">
              <button
                className="px-4 py-2 rounded-md bg-gray-200 text-gray-800 font-medium hover:bg-gray-300"
                onClick={() => setDeliveryModalOpen(false)}
              >
                Cancel
              </button>
              <button
                className="px-4 py-2 rounded-md bg-green-600 text-white font-medium hover:bg-green-700"
                onClick={async () => {
                  // Geofencing for delivery: must be within 2km of delivery location
                  if (!reservedTransaction) return;
                  // Find the delivery location coordinates from the selected delivery location
                  const order = orders.find(o => o._id === reservedTransaction.purchaseOrderId);
                  if (!order) return alert('Order not found.');
                  // Find the delivery location in the order's loads
                  let deliveryLat = null, deliveryLng = null;
                  for (const load of order.loads) {
                    if (load.deliveryLocation === reservedTransaction.deliveryLocationName) {
                      // Try to find coordinates in the deliveryLocation string (if available)
                      // If you have a mapping of delivery locations to coordinates, use it here
                      // For now, fallback to pickup location coordinates if not found
                      // TODO: Replace with actual delivery location coordinates if available
                      deliveryLat = order.pickupLocation.coordinates.lat;
                      deliveryLng = order.pickupLocation.coordinates.lng;
                      break;
                    }
                  }
                  if (deliveryLat === null || deliveryLng === null) {
                    alert('Delivery location coordinates not found.');
                    return;
                  }
                  const withinGeofence = await checkGeofence(deliveryLat, deliveryLng);
                  if (!withinGeofence) return;
                  const token = localStorage.getItem("token");
                  const headers = token ? { Authorization: `Bearer ${token}` } : {};

                  const formData = new FormData();
                  const sigData = deliverySignaturePadRef.current && !deliverySignaturePadRef.current.isEmpty() ? deliverySignaturePadRef.current.getCanvas().toDataURL('image/png') : '';
                  formData.append('signature', sigData);
                  formData.append('materialsData', JSON.stringify(pickupQuantities.map(q => {
                    const locationName = reservedTransaction.deliveryLocationName;
                    return { ...q, deliveryLocationName: locationName };
                  })));
                  formData.append('action', 'delivery');
                  if (pickupTicket) {
                    formData.append('ticket', pickupTicket);
                  }

                  try {
                    const response = await axios.post(
                      `/api/purchase-orders/${reservedTransaction.purchaseOrderId}/complete/${reservedTransaction.transactionId}`,
                      formData,
                      { headers }
                    );

                    // Handle warnings and completion message
                    let message = response.data.message;
                    if (response.data.warnings && response.data.warnings.length > 0) {
                      const warningMessages = response.data.warnings.map((w: any) =>
                        `${w.materialName}: Over-delivered by ${w.overage} units (requested: ${w.requested}, delivered: ${w.delivered})`
                      ).join('\n');
                      message += '\n\nWarnings:\n' + warningMessages;
                    }
                    if (response.data.completionMessage) {
                      message += '\n\n' + response.data.completionMessage;
                    }

                    alert(message);
                    setReservedTransaction(null);
                    setDeliveryModalOpen(false);
                    setPickupCompleted(false);

                    // Immediately update the orders list to mark this PO as completed
                    setOrders(prevOrders => prevOrders.map(order =>
                      order._id === reservedTransaction?.purchaseOrderId
                        ? { ...order, isCompleted: true }
                        : order
                    ));

                    fetchCompletedTransactions();
                    fetchActiveReservation();
                    fetchOrders(); // Keep this as a backup to ensure server-side sync
                  } catch (error) {
                    console.error('Failed to complete delivery:', error);
                    alert('Failed to complete delivery');
                  }
                }}
              >
                Submit
              </button>
            </div>
          </div>
        </div>
      )}

      {/* View Signature Modal */}
      {viewSignatureModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white p-6 rounded-xl shadow-lg w-[90%] max-w-md max-h-[90vh] overflow-auto">
            <h3 className="text-xl font-semibold mb-4 text-[var(--text-primary)]">
              {viewingType === 'pickup' ? 'Pickup' : 'Delivery'} Signature
            </h3>

            <div className="flex justify-center mb-4">
              <img
                src={viewingSignature}
                alt={`${viewingType} signature`}
                className="max-w-full max-h-64 border border-gray-300 rounded"
              />
            </div>

            <div className="flex justify-end">
              <button
                className="px-4 py-2 rounded-md bg-gray-200 text-gray-800 font-medium hover:bg-gray-300"
                onClick={() => setViewSignatureModal(false)}
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}

      {/* View Ticket Modal */}
      {viewTicketModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white p-6 rounded-xl shadow-lg w-[90%] max-w-md max-h-[90vh] overflow-auto">
            <h3 className="text-xl font-semibold mb-4 text-[var(--text-primary)]">
              {viewingType === 'pickup' ? 'Pickup' : 'Delivery'} Ticket
            </h3>

            <div className="flex justify-center mb-4">
              <img
                src={`data:image/jpeg;base64,${viewingTicket}`}
                alt={`${viewingType} ticket`}
                className="max-w-full max-h-64 border border-gray-300 rounded"
              />
            </div>

            <div className="flex justify-end">
              <button
                className="px-4 py-2 rounded-md bg-gray-200 text-gray-800 font-medium hover:bg-gray-300"
                onClick={() => setViewTicketModal(false)}
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Cancel Reservation Modal */}
      {showCancelModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white p-6 rounded-xl shadow-lg w-[90%] max-w-md">
            <h3 className="text-xl font-semibold mb-4 text-[var(--text-primary)]">
              Cancel Reservation
            </h3>
            <p className="text-sm text-gray-600 mb-4">
              Are you sure you want to cancel this reservation? This action cannot be undone.
            </p>
            <div className="flex justify-end gap-3">
              <button
                className="px-4 py-2 rounded-md bg-gray-200 text-gray-800 font-medium hover:bg-gray-300"
                onClick={() => setShowCancelModal(false)}
              >
                No, Keep It
              </button>
              <button
                className="px-4 py-2 rounded-md bg-red-600 text-white font-medium hover:bg-red-700"
                onClick={async () => {
                  try {
                    const token = localStorage.getItem("token");
                    const headers = token ? { Authorization: `Bearer ${token}` } : {};

                    await axios.post(
                      `/api/purchase-orders/${reservedTransaction?.purchaseOrderId}/cancel/${reservedTransaction?.transactionId}`,
                      {},
                      { headers }
                    );

                    setShowCancelModal(false);
                    setReservedTransaction(null);
                    setPickupCompleted(false);
                    fetchOrders();
                  } catch (error) {
                    console.error('Failed to cancel reservation:', error);
                    alert('Failed to cancel reservation');
                  }
                }}
              >
                Yes, Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {showDocsModal && (
        <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-md relative p-6 flex flex-col gap-4 max-h-[90vh] overflow-y-auto">
            <button className="absolute top-4 right-4 text-gray-500 hover:text-gray-800 text-2xl" onClick={() => setShowDocsModal(false)}>&times;</button>
            <h2 className="text-lg font-semibold mb-4">Driver Documents (optional, but encouraged)</h2>
            <div className="flex flex-col gap-6">
              <div>
                <label className="block font-semibold mb-1">Driver's License Photo</label>
                {user?.licensePhotoUrl && (
                  <img
                    src={user.licensePhotoUrl.startsWith('http') ? user.licensePhotoUrl : `${window.location.origin}${user.licensePhotoUrl}`}
                    alt="License"
                    className="mb-2 rounded shadow max-h-32"
                  />
                )}
                <div className="flex gap-2 items-center">
                  <input
                    type="file"
                    accept="image/*"
                    onChange={e => setLicensePhoto(e.target.files?.[0] || null)}
                    className="block w-full text-sm text-gray-700 border border-gray-300 rounded cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-400"
                  />
                  <button
                    className="px-3 py-1 bg-blue-600 text-white rounded font-semibold hover:bg-blue-700 disabled:opacity-50"
                    onClick={() => { handleUpload('license'); setLicensePhoto(null); }}
                    disabled={!licensePhoto || uploading}
                    type="button"
                  >
                    Upload
                  </button>
                </div>
              </div>
              <div>
                <label className="block font-semibold mb-1">Insurance Photo</label>
                {user?.insurancePhotoUrl && (
                  <img
                    src={user.insurancePhotoUrl.startsWith('http') ? user.insurancePhotoUrl : `${window.location.origin}${user.insurancePhotoUrl}`}
                    alt="Insurance"
                    className="mb-2 rounded shadow max-h-32"
                  />
                )}
                <div className="flex gap-2 items-center">
                  <input
                    type="file"
                    accept="image/*"
                    onChange={e => setInsurancePhoto(e.target.files?.[0] || null)}
                    className="block w-full text-sm text-gray-700 border border-gray-300 rounded cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-400"
                  />
                  <button
                    className="px-3 py-1 bg-blue-600 text-white rounded font-semibold hover:bg-blue-700 disabled:opacity-50"
                    onClick={() => { handleUpload('insurance'); setInsurancePhoto(null); }}
                    disabled={!insurancePhoto || uploading}
                    type="button"
                  >
                    Upload
                  </button>
                </div>
              </div>
            </div>
            {uploadMsg && <div className="mt-2 text-sm text-blue-700">{uploadMsg}</div>}
          </div>
        </div>
      )}
    </div>
  );
};

const SummaryCard: React.FC<{ label: string; value: string }> = ({ label, value }) => (
  <div className="flex flex-col gap-1 rounded-xl p-4 border border-[var(--surface-border)] bg-white shadow-sm hover:shadow-md transition-shadow">
    <p className="text-sm font-medium text-[var(--text-secondary)]">{label}</p>
    <p className="text-3xl font-bold text-[var(--text-primary)]">{value}</p>
  </div>
);

const OrderCard: React.FC<{
  id: string;
  client: string;
  pickupLocation: string;
  loads: Load[];
  isCompleted?: boolean;
  t?: any;
}> = ({ id, client, pickupLocation, loads, isCompleted, t }) => {
  // Calculate total cost across all loads
  const totalCost = loads.reduce((sum, load) =>
    sum + load.materials.reduce((loadSum, m) => {
      // Simple cost calculation based on quantity and type
      const baseCost = m.loadType === 'tons' ? 50 : 40; // $50 per ton, $40 per cubic yard
      return loadSum + (m.quantity * baseCost);
    }, 0), 0
  );
  const price = `$${totalCost.toFixed(2)}`;

  if (isCompleted) {
    return (
      <div className="bg-green-50 p-4 rounded-2xl border border-green-200 shadow-md mb-4 flex flex-col gap-2">
        <div className="flex items-center gap-2">
          <span className="material-icons text-green-600">check_circle</span>
          <span className="font-semibold text-green-800">{t ? t('dashboard.completedOrder') : '✓ This PO has been completed'}</span>
        </div>
        <div className="text-base font-medium text-green-800">{t ? t('dashboard.orderNumber', { number: id.slice(-5).toUpperCase() }) : `Order #${id.slice(-5).toUpperCase()}`}</div>
        <div className="text-sm text-green-600">{t ? t('dashboard.client') : 'Client'}: {client}</div>
      </div>
    );
  }

  return (
    <div className="bg-white p-6 rounded-2xl border border-[var(--surface-border)] shadow-md flex flex-col gap-4">
      <div className="flex items-center gap-2 mb-2">
        <span className="material-icons text-indigo-500">assignment</span>
        <span className="text-lg font-bold text-[var(--text-primary)]">{t ? t('dashboard.orderNumber', { number: id.slice(-5).toUpperCase() }) : `Order #${id.slice(-5).toUpperCase()}`}</span>
      </div>
      <div className="flex flex-col gap-1 mb-2">
        <span className="text-sm text-gray-700"><span className="font-semibold">{t ? t('dashboard.client') : 'Client'}:</span> {client}</span>
        <span className="text-sm text-gray-700"><span className="font-semibold">{t ? t('dashboard.pickupLocation') : 'Pickup Location'}:</span> {pickupLocation}</span>
      </div>
      <div className="mb-2">
        <span className="block text-sm font-semibold text-[var(--text-primary)] mb-1">{t ? t('dashboard.deliveryLocationsMaterials') : 'Delivery Locations & Materials:'}</span>
        <div className="space-y-2">
        {loads.map((load, idx) => (
            <div key={`${load.deliveryLocation}-${idx}`} className="bg-gray-50 rounded-lg p-3">
              <div className="flex items-center gap-1 mb-1">
                <span className="material-icons text-blue-400 text-base">place</span>
                <span className="font-medium text-blue-700">{load.deliveryLocation}</span>
              </div>
              <div className="pl-4 space-y-1">
              {load.materials.map((material, midx) => (
                  <div key={`${material.materialId}-${midx}`} className="text-sm text-gray-700">{material.materialName}</div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

const HistoryCard: React.FC<{
  number: string;
  customer: string;
  amount: string;
  status: string;
  purchaseOrderId: string;
  transactionId: string;
  deliveredMaterials: any[];
  onViewSignature: (type: 'pickup' | 'delivery', purchaseOrderId?: string, transactionId?: string) => void;
  onViewTicket: (type: 'pickup' | 'delivery', purchaseOrderId?: string, transactionId?: string) => void;
  t?: any;
}> = ({ number, customer, amount, status, purchaseOrderId, transactionId, deliveredMaterials, onViewSignature, onViewTicket, t }) => {
  return (
    <div className="bg-white p-4 rounded-lg border border-[var(--surface-border)] shadow-sm mb-4">
      <div className="flex items-center gap-2 mb-2">
        <span className="material-icons text-blue-500">assignment</span>
        <span className="font-semibold text-blue-800">{number}</span>
      </div>
      <div className="flex flex-col gap-1 mb-2">
        <span className="text-sm text-gray-700"><span className="font-semibold">{t ? t('dashboard.client') : 'Client'}:</span> {customer}</span>
        <span className="text-sm text-gray-700"><span className="font-semibold">{t ? t('dashboard.amount') : 'Amount'}:</span> {amount}</span>
        <span className="text-sm text-gray-700"><span className="font-semibold">{t ? t('dashboard.status') : 'Status'}:</span> {status}</span>
      </div>
      <div className="flex justify-end gap-2">
        <button
          className="px-4 py-2 rounded-md bg-gray-200 text-gray-800 font-medium hover:bg-gray-300"
          onClick={() => onViewSignature('pickup', purchaseOrderId, transactionId)}
        >
          View Pickup Signature
        </button>
        <button
          className="px-4 py-2 rounded-md bg-gray-200 text-gray-800 font-medium hover:bg-gray-300"
          onClick={() => onViewTicket('pickup', purchaseOrderId, transactionId)}
        >
          View Pickup Ticket
        </button>
      </div>
    </div>
  );
};

export default Dashboard;