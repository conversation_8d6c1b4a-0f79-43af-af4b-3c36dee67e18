import React, { useState, useEffect } from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, Ta<PERSON>Content } from "@/components/ui/tabs";
import PoCreate from './PoCreate';
import { useTranslation } from 'react-i18next';
import LanguageSwitcher from '../components/LanguageSwitcher';
import { useNavigate } from 'react-router-dom';
import ClientManagement from '../components/ClientManagement';
import JobManagement from '../components/JobManagement';
import axios from 'axios';
import LocationInput from '../components/LocationInput';

interface PurchaseOrder {
  _id: string;          // Use _id from MongoDB
  orderNumber: number;
  clientName: string;
  jobName?: string;     // Add job name field
  pickupLocation: {
    name: string;
    address: string;
    coordinates: { lat: number; lng: number };
  };
  deliveryLocations: { id: number; name: string; address: string }[];
  materials: { id: number; name: string; description: string; cost: number; quantity: number; loadType: string; rate?: number }[];
  createdAt: string;
  isCompleted: boolean;
  assignedTo?: string;
  loads?: {
    deliveryLocation: LocationData | string;
    materials: {
      materialId: string;
      materialName: string;
      quantity: number;
      loadType: string;
      remainingQuantity?: number;
    }[];
  }[];
  transactions?: {
    _id: string;
    truckerId: {
      username?: string;
      email?: string;
      toString?: () => string;
    };
    status: string;
    reservedAt?: string;
    pickedUpAt?: string;
    completedAt?: string;
    reservedMaterials?: {
      materialName: string;
      quantity: number;
      deliveryLocationName: string;
    }[];
    materialsPickedUp?: {
      materialName?: string;
      materialId?: string;
      quantity: number;
      deliveryLocationName: string;
    }[];
    materialsDelivered?: {
      materialName?: string;
      materialId?: string;
      quantity: number;
      deliveryLocationName: string;
    }[];
    signature?: string;
    pickupTicketImage?: string;
    deliverySignature?: string;
    deliveryTicketImage?: string;
  }[];
}

// LocationData interface for type safety
interface LocationData {
  street: string;
  city: string;
  postalCode: string;
  latitude: number;
  longitude: number;
  state?: string;
}

// Helper to convert a string location to a LocationData object (fallback for legacy data)
const stringToLocationData = (str: string | undefined | null): LocationData => {
  if (typeof str !== 'string' || !str) {
    return { street: '', city: '', state: '', postalCode: '', latitude: 0, longitude: 0 };
  }
  // Try to parse addresses like '123 Main St, Miami, FL 33101' or '123 Main St, Miami, FL, 33101'
  const parts = str.split(',').map(p => p.trim());
  let street = '', city = '', state = '', postalCode = '';
  if (parts.length === 1) {
    street = parts[0];
  } else if (parts.length === 2) {
    street = parts[0];
    city = parts[1];
  } else if (parts.length === 3) {
    street = parts[0];
    city = parts[1];
    // Try to split state and postal from the last part
    const stateZip = parts[2].match(/^([A-Z]{2})\s*(\d{5}(?:-\d{4})?)?$/);
    if (stateZip) {
      state = stateZip[1];
      postalCode = stateZip[2] || '';
    } else {
      state = parts[2];
    }
  } else if (parts.length >= 4) {
    street = parts[0];
    city = parts[1];
    state = parts[2];
    postalCode = parts[3];
  }
  return {
    street,
    city,
    state,
    postalCode,
    latitude: 0,
    longitude: 0,
  };
};

// Helper to check if a value is a LocationData object
function isLocationData(val: any): val is LocationData {
  return val && typeof val === 'object' && typeof val !== 'string' && (
    'street' in val || 'city' in val || 'latitude' in val || 'longitude' in val
  );
}

// Helper to safely extract address from delivery location
function getDeliveryLocationAddress(deliveryLocation: any): string {
  if (typeof deliveryLocation === 'string') {
    return deliveryLocation;
  }

  // Check if it's an object with location fields (even if some are undefined)
  if (deliveryLocation && typeof deliveryLocation === 'object' && deliveryLocation !== null) {
    const parts = [
      deliveryLocation.street,
      deliveryLocation.city,
      deliveryLocation.state,
      deliveryLocation.postalCode
    ].filter(Boolean);

    if (parts.length > 0) {
      return parts.join(', ');
    }

    // If no standard fields, check for alternative field names
    const altParts = [
      deliveryLocation.address1,
      deliveryLocation.address,
      deliveryLocation.location,
      deliveryLocation.name
    ].filter(Boolean);

    if (altParts.length > 0) {
      return altParts.join(', ');
    }
  }

  return 'No address specified';
}

// Helper to geocode an address string to LocationData (async)
const geocodeAddressToLocationData = async (address: string): Promise<Partial<LocationData>> => {
  try {
    const response = await fetch('/api/geocode', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ address }),
    });
    if (response.ok) {
      const result = await response.json();
      // Use the same parse logic as LocationInput
      const parts = address.split(',').map(p => p.trim());
      let street = '', city = '', state = '', postalCode = '';
      if (parts.length === 1) {
        street = parts[0];
      } else if (parts.length === 2) {
        street = parts[0];
        city = parts[1];
      } else if (parts.length === 3) {
        street = parts[0];
        city = parts[1];
        const stateZip = parts[2].match(/^([A-Z]{2})\s*(\d{5}(?:-\d{4})?)?$/);
        if (stateZip) {
          state = stateZip[1];
          postalCode = stateZip[2] || '';
        } else {
          state = parts[2];
        }
      } else if (parts.length >= 4) {
        street = parts[0];
        city = parts[1];
        state = parts[2];
        postalCode = parts[3];
      }
      return {
        street,
        city,
        state,
        postalCode,
        latitude: result.lat,
        longitude: result.lng,
      };
    }
  } catch (e) {}
  return {};
};

// --- Trucker Organization Page ---
const TruckerOrganization: React.FC = () => {
  const [truckers, setTruckers] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [newUsername, setNewUsername] = useState('');
  const [newEmail, setNewEmail] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [creating, setCreating] = useState(false);
  const [success, setSuccess] = useState<string | null>(null);
  const [showPassword, setShowPassword] = useState(false);
  const [newLicenseExp, setNewLicenseExp] = useState('');
  const [newInsuranceExp, setNewInsuranceExp] = useState('');
  const [newStatus, setNewStatus] = useState('Active');
  const [editDriver, setEditDriver] = useState<any | null>(null);
  const [statusFilter, setStatusFilter] = useState('All');
  const [showAddForm, setShowAddForm] = useState(false);
  const [showUpcomingExpirations, setShowUpcomingExpirations] = useState(false);

  const fetchTruckers = async () => {
    setLoading(true);
    setError(null);
    try {
      const token = localStorage.getItem('token');
      const headers: Record<string, string> = {};
      if (token) headers['Authorization'] = `Bearer ${token}`;
      const res = await fetch('/api/auth/users?role=trucker', { headers });
      if (!res.ok) throw new Error('Failed to fetch truckers');
      const data = await res.json();
      setTruckers(data);
    } catch (err) {
      setError('Failed to fetch truckers');
    }
    setLoading(false);
  };

  useEffect(() => { fetchTruckers(); }, []);

  const handleCreate = async (e: React.FormEvent) => {
    e.preventDefault();
    setCreating(true);
    setError(null);
    setSuccess(null);
    try {
      const token = localStorage.getItem('token');
      const headers: Record<string, string> = { 'Content-Type': 'application/json' };
      if (token) headers['Authorization'] = `Bearer ${token}`;
      const res = await fetch('/api/auth/users', {
        method: 'POST',
        headers,
        body: JSON.stringify({ username: newUsername, email: newEmail, password: newPassword, role: 'trucker', licenseExp: newLicenseExp, insuranceExp: newInsuranceExp, status: newStatus })
      });
      if (!res.ok) {
        const data = await res.json();
        throw new Error(data.message || 'Failed to create trucker');
      }
      setNewUsername('');
      setNewEmail('');
      setNewPassword('');
      setNewLicenseExp('');
      setNewInsuranceExp('');
      setNewStatus('Active');
      setSuccess('Trucker created successfully');
      fetchTruckers();
    } catch (err: any) {
      setError(err.message || 'Failed to create trucker');
    }
    setCreating(false);
  };

  const handleDelete = async (id: string) => {
    if (!window.confirm('Delete this trucker?')) return;
    setError(null);
    try {
      const token = localStorage.getItem('token');
      const headers: Record<string, string> = {};
      if (token) headers['Authorization'] = `Bearer ${token}`;
      const res = await fetch(`/api/auth/users/${id}`, { method: 'DELETE', headers });
      if (!res.ok) throw new Error('Failed to delete trucker');
      fetchTruckers();
    } catch (err) {
      setError('Failed to delete trucker');
    }
  };

  const handleDeactivate = async (driver: any) => {
    if (!window.confirm('Deactivate this driver?')) return;
    setError(null);
    try {
      const token = localStorage.getItem('token');
      const headers: Record<string, string> = { 'Content-Type': 'application/json' };
      if (token) headers['Authorization'] = `Bearer ${token}`;
      const res = await fetch(`/api/auth/users/${driver._id}`, {
        method: 'PATCH',
        headers,
        body: JSON.stringify({ status: 'Inactive' })
      });
      if (!res.ok) throw new Error('Failed to deactivate driver');
      fetchTruckers();
    } catch (err) {
      setError('Failed to deactivate driver');
    }
  };

  const handleEdit = (driver: any) => {
    setEditDriver(driver);
  };

  const handleEditSave = async () => {
    if (!editDriver) return;
    setError(null);
    try {
      const token = localStorage.getItem('token');
      const headers: Record<string, string> = { 'Content-Type': 'application/json' };
      if (token) headers['Authorization'] = `Bearer ${token}`;
      const res = await fetch(`/api/auth/users/${editDriver._id}`, {
        method: 'PATCH',
        headers,
        body: JSON.stringify({
          username: editDriver.username,
          email: editDriver.email,
          licenseExp: editDriver.licenseExp,
          insuranceExp: editDriver.insuranceExp,
          status: editDriver.status
        })
      });
      if (!res.ok) throw new Error('Failed to update driver');
      setEditDriver(null);
      fetchTruckers();
    } catch (err) {
      setError('Failed to update driver');
    }
  };

  const handleExportExpired = () => {
    const today = new Date().toISOString().split('T')[0];
    const expired = truckers.filter(t => (t.licenseExp && t.licenseExp < today) || (t.insuranceExp && t.insuranceExp < today));
    const csvRows = [
      ['Username', 'Email', 'Status', 'License Exp.', 'Insurance Exp.'],
      ...expired.map(t => [t.username, t.email, t.status, t.licenseExp, t.insuranceExp])
    ];
    const csvContent = csvRows.map(e => e.join(",")).join("\n");
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'expired_drivers.csv';
    a.click();
    URL.revokeObjectURL(url);
  };

  const getUpcomingExpirations = () => {
    const today = new Date();
    const in30 = new Date();
    in30.setDate(today.getDate() + 30);
    return truckers.filter(t => {
      const lic = t.licenseExp ? new Date(t.licenseExp) : null;
      const ins = t.insuranceExp ? new Date(t.insuranceExp) : null;
      return (lic && lic >= today && lic <= in30) || (ins && ins >= today && ins <= in30);
    });
  };

  const filteredTruckers = statusFilter === 'All' ? truckers : truckers.filter(t => t.status === statusFilter);

  // Reset add driver form fields when opening the form
  useEffect(() => {
    if (showAddForm) {
      setNewUsername('');
      setNewEmail('');
      setNewPassword('');
      setNewLicenseExp('');
      setNewInsuranceExp('');
      setNewStatus('Active');
    }
  }, [showAddForm]);

  return (
    <div className="p-4 w-screen" style={{marginLeft:0, paddingLeft:0}}>
      <h2 className="text-2xl font-bold mb-4">Drivers</h2>
      {!showAddForm && (
        <button className="mb-4 bg-blue-600 text-white px-4 py-2 rounded font-semibold hover:bg-blue-700" onClick={() => setShowAddForm(true)}>
          Add a new driver
        </button>
      )}
      {showAddForm && (
        <form className="mb-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 items-end" onSubmit={handleCreate}>
          <div>
            <label className="block font-semibold mb-1">Username</label>
            <input className="border rounded px-2 py-1 w-full" type="text" value={newUsername} onChange={e => setNewUsername(e.target.value)} required placeholder="Enter username" />
          </div>
          <div>
            <label className="block font-semibold mb-1">Email</label>
            <input className="border rounded px-2 py-1 w-full" type="email" value={newEmail} onChange={e => setNewEmail(e.target.value)} required placeholder="Enter email" />
          </div>
          <div>
            <label className="block font-semibold mb-1">Password</label>
            <div className="flex items-center">
              <input className="border rounded px-2 py-1 w-full" type={showPassword ? 'text' : 'password'} value={newPassword} onChange={e => setNewPassword(e.target.value)} required />
              <button type="button" className="ml-2 text-xs text-blue-600 underline" onClick={() => setShowPassword(!showPassword)}>{showPassword ? 'Hide' : 'Show'}</button>
            </div>
          </div>
          <div>
            <label className="block font-semibold mb-1">License Expiration</label>
            <input className="border rounded px-2 py-1 w-full" type="date" value={newLicenseExp} onChange={e => setNewLicenseExp(e.target.value)} required />
          </div>
          <div>
            <label className="block font-semibold mb-1">Insurance Expiration</label>
            <input className="border rounded px-2 py-1 w-full" type="date" value={newInsuranceExp} onChange={e => setNewInsuranceExp(e.target.value)} required />
          </div>
          <div>
            <label className="block font-semibold mb-1">Status</label>
            <select className="border rounded px-2 py-1 w-full" value={newStatus} onChange={e => setNewStatus(e.target.value)} required>
              <option value="Active">Active</option>
              <option value="Inactive">Inactive</option>
              <option value="Pending">Pending</option>
            </select>
          </div>
          <div className="md:col-span-2 lg:col-span-3 flex gap-2 mt-2">
            <button className="bg-blue-600 text-white px-4 py-2 rounded font-semibold hover:bg-blue-700 w-full md:w-auto" type="submit" disabled={creating}>Add Driver</button>
            <button type="button" className="bg-gray-300 px-4 py-2 rounded font-semibold hover:bg-gray-400 w-full md:w-auto" onClick={() => setShowAddForm(false)}>Cancel</button>
          </div>
        </form>
      )}
      {success && <div className="mb-2 text-green-700">{success}</div>}
      {error && <div className="mb-2 text-red-700">{error}</div>}
      {/* Controls: filter and export */}
      <div className="flex flex-col sm:flex-row sm:items-center mb-4 gap-2 sm:gap-4">
        <div className="flex items-center gap-2">
          <label className="font-semibold">Filter by Status:</label>
          <select className="border rounded px-2 py-1" value={statusFilter} onChange={e => setStatusFilter(e.target.value)}>
            <option value="All">All</option>
            <option value="Active">Active</option>
            <option value="Inactive">Inactive</option>
            <option value="Pending">Pending</option>
          </select>
        </div>
        <button className="bg-green-600 text-white px-3 py-1 rounded text-sm font-semibold hover:bg-green-700 w-fit" onClick={handleExportExpired} type="button">Export Expired</button>
        <button className="bg-yellow-500 text-white px-3 py-1 rounded text-sm font-semibold hover:bg-yellow-600 w-fit" onClick={() => setShowUpcomingExpirations(true)} type="button">Upcoming Expirations</button>
      </div>
      {/* Upcoming Expirations Modal */}
      {showUpcomingExpirations && (
        <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded shadow-lg max-w-lg w-full">
            <h3 className="text-lg font-bold mb-4">Upcoming Expirations (Next 30 Days)</h3>
            <ul className="mb-4">
              {getUpcomingExpirations().length === 0 ? (
                <li className="text-gray-500">No upcoming expirations.</li>
              ) : getUpcomingExpirations().map(t => (
                <li key={t._id} className="mb-2 flex flex-col border-b pb-2">
                  <span className="font-semibold">{t.username} ({t.email})</span>
                  <span>License Exp: {t.licenseExp || 'N/A'}</span>
                  <span>Insurance Exp: {t.insuranceExp || 'N/A'}</span>
                </li>
              ))}
            </ul>
            <div className="flex justify-end">
              <button className="bg-gray-300 px-4 py-2 rounded font-semibold" onClick={() => setShowUpcomingExpirations(false)}>Close</button>
            </div>
          </div>
        </div>
      )}
      {/* Table wrapper with improved responsiveness and compact style */}
      <div className="w-full overflow-x-auto rounded shadow bg-white max-w-full">
        <table className="w-full text-xs table-fixed">
          <thead className="bg-gray-100">
            <tr>
              <th className="p-1 text-left w-16">User</th>
              <th className="p-1 text-left w-32">Email</th>
              <th className="p-1 text-left w-12">Role</th>
              <th className="p-1 text-center w-8">Lic</th>
              <th className="p-1 text-center w-8">Ins</th>
              <th className="p-1 text-center w-12">L.Img</th>
              <th className="p-1 text-center w-12">I.Img</th>
              <th className="p-1 text-left w-12">Status</th>
              <th className="p-1 text-left w-16">L.Exp</th>
              <th className="p-1 text-left w-16">I.Exp</th>
              <th className="p-1 text-left w-16">Actions</th>
            </tr>
          </thead>
          <tbody>
            {loading ? (
              <tr><td colSpan={9} className="p-4 text-center">Loading...</td></tr>
            ) : filteredTruckers.length === 0 ? (
              <tr><td colSpan={9} className="p-4 text-center">No drivers found.</td></tr>
            ) : filteredTruckers.map(trucker => (
              <tr key={trucker._id} className="border-b hover:bg-gray-50">
                <td className="p-1 font-mono text-xs truncate max-w-0">{trucker.username}</td>
                <td className="p-1 text-xs truncate max-w-0" title={trucker.email}>{trucker.email}</td>
                <td className="p-1 text-xs">{trucker.role}</td>
                <td className="p-1 text-center text-xs">{trucker.license ? '📄' : '-'}</td>
                <td className="p-1 text-center text-xs">{trucker.insurance ? '📄' : '-'}</td>
                <td className="p-1 text-center">
                  {trucker.licensePhotoUrl ? (
                    <a href={trucker.licensePhotoUrl.startsWith('http') ? trucker.licensePhotoUrl : `${window.location.origin}${trucker.licensePhotoUrl}`}
                       target="_blank" rel="noopener noreferrer">
                      <img src={trucker.licensePhotoUrl.startsWith('http') ? trucker.licensePhotoUrl : `${window.location.origin}${trucker.licensePhotoUrl}`}
                           alt="License" className="h-6 w-8 object-cover rounded shadow mx-auto" />
                    </a>
                  ) : (
                    <span className="text-gray-400 text-xs">-</span>
                  )}
                </td>
                <td className="p-1 text-center">
                  {trucker.insurancePhotoUrl ? (
                    <a href={trucker.insurancePhotoUrl.startsWith('http') ? trucker.insurancePhotoUrl : `${window.location.origin}${trucker.insurancePhotoUrl}`}
                       target="_blank" rel="noopener noreferrer">
                      <img src={trucker.insurancePhotoUrl.startsWith('http') ? trucker.insurancePhotoUrl : `${window.location.origin}${trucker.insurancePhotoUrl}`}
                           alt="Insurance" className="h-6 w-8 object-cover rounded shadow mx-auto" />
                    </a>
                  ) : (
                    <span className="text-gray-400 text-xs">-</span>
                  )}
                </td>
                <td className="p-1 text-xs">{trucker.status}</td>
                <td className="p-1 text-xs">{trucker.licenseExp}</td>
                <td className="p-1 text-xs">{trucker.insuranceExp}</td>
                <td className="p-1">
                  <div className="flex flex-col gap-0.5">
                    <button className="text-blue-600 hover:underline text-xs" onClick={() => handleEdit(trucker)}>Edit</button>
                    <button className="text-yellow-600 hover:underline text-xs" onClick={() => handleDeactivate(trucker)}>Deact</button>
                    <button className="text-red-600 hover:underline text-xs" onClick={() => handleDelete(trucker._id)}>Del</button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      {editDriver && (
        <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded shadow-lg max-w-md w-full">
            <h3 className="text-lg font-bold mb-4">Edit Driver</h3>
            <div className="mb-2">
              <label className="block font-semibold mb-1">Username</label>
              <input className="border rounded px-2 py-1 w-full" type="text" value={editDriver.username} onChange={e => setEditDriver({ ...editDriver, username: e.target.value })} />
            </div>
            <div className="mb-2">
              <label className="block font-semibold mb-1">Email</label>
              <input className="border rounded px-2 py-1 w-full" type="email" value={editDriver.email} onChange={e => setEditDriver({ ...editDriver, email: e.target.value })} />
            </div>
            <div className="mb-2">
              <label className="block font-semibold mb-1">License Expiration</label>
              <input className="border rounded px-2 py-1 w-full" type="date" value={editDriver.licenseExp} onChange={e => setEditDriver({ ...editDriver, licenseExp: e.target.value })} />
            </div>
            <div className="mb-2">
              <label className="block font-semibold mb-1">Insurance Expiration</label>
              <input className="border rounded px-2 py-1 w-full" type="date" value={editDriver.insuranceExp} onChange={e => setEditDriver({ ...editDriver, insuranceExp: e.target.value })} />
            </div>
            <div className="mb-4">
              <label className="block font-semibold mb-1">Status</label>
              <select className="border rounded px-2 py-1 w-full" value={editDriver.status} onChange={e => setEditDriver({ ...editDriver, status: e.target.value })}>
                <option value="Active">Active</option>
                <option value="Inactive">Inactive</option>
                <option value="Pending">Pending</option>
              </select>
            </div>
            <div className="flex justify-end gap-2">
              <button className="bg-gray-300 px-3 py-1 rounded" onClick={() => setEditDriver(null)}>Cancel</button>
              <button className="bg-blue-600 text-white px-3 py-1 rounded" onClick={handleEditSave}>Save</button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

const AdminDashboard = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [tab, setTab] = useState('dashboard');
  const [purchaseOrders, setPurchaseOrders] = useState<PurchaseOrder[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [modalOpen, setModalOpen] = useState(false);
  const [modalImage, setModalImage] = useState<string | null>(null);
  const [modalTitle, setModalTitle] = useState<string>('');
  const [loadingImage, setLoadingImage] = useState(false);
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [editPo, setEditPo] = useState<PurchaseOrder | null>(null);
  const [editLoad, setEditLoad] = useState<any>(null);
  const [editMaterials, setEditMaterials] = useState<any[]>([]);
  const [expandedTransactions, setExpandedTransactions] = useState<{ [poId: string]: boolean }>({});
  const [filter, setFilter] = useState<'day' | 'week' | 'month'>('day');
  
  // New state for trucker assignment
  const [truckers, setTruckers] = useState<any[]>([]);
  const [assignModalOpen, setAssignModalOpen] = useState(false);
  const [selectedPoForAssignment, setSelectedPoForAssignment] = useState<PurchaseOrder | null>(null);
  const [selectedTrucker, setSelectedTrucker] = useState<string>('');
  const [selectedDeliveryLocation, setSelectedDeliveryLocation] = useState<string>('');
  const [selectedMaterials, setSelectedMaterials] = useState<{ materialId: string; quantity: number }[]>([]);

  // New state for editing loads (delivery locations) in the edit modal
  const [editLoads, setEditLoads] = useState<any[]>([]);

  // New filter states
  const [poClientFilter, setPoClientFilter] = useState('');
  const [poJobFilter, setPoJobFilter] = useState('');
  const [poJobNameFilter, setPoJobNameFilter] = useState('');
  const [poNumberFilter, setPoNumberFilter] = useState('');
  const [showAllPOs, setShowAllPOs] = useState(false);

  // Helper: get date range for filter
  const getDateRange = () => {
    const now = new Date();
    if (filter === 'day') {
      const start = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      return { start, end: now };
    } else if (filter === 'week') {
      const start = new Date(now);
      start.setDate(now.getDate() - 6);
      start.setHours(0, 0, 0, 0);
      return { start, end: now };
    } else {
      const start = new Date(now.getFullYear(), now.getMonth(), 1);
      return { start, end: now };
    }
  };
  const { start, end } = getDateRange();

  // Filtered POs for summary
  const filteredPOs = purchaseOrders.filter(po => {
    const created = new Date(po.createdAt);
    return created >= start && created <= end;
  });

  // Summary calculations
  const totalPickups = filteredPOs.length;
  const totalDeliveries = filteredPOs.reduce((sum, po) => sum + (po.loads?.reduce((s, l) => s + (l.materials?.length || 0), 0) || 0), 0);
  const activeReservations = filteredPOs.reduce((sum, po) => sum + (po.transactions?.filter(tx => tx.status !== 'Completed').length || 0), 0);
  const loadsInTransit = filteredPOs.reduce((sum, po) => sum + (po.transactions?.filter(tx => tx.status === 'PickedUp').length || 0), 0);
  const dollarsEarned = filteredPOs.reduce((sum, po) => sum + (po.materials?.reduce((s, m) => s + ((m.quantity || 0) * (m.rate || 0)), 0) || 0), 0);

  // Fetch purchase orders
  const fetchPurchaseOrders = async () => {
    setLoading(true);
    setError(null);
    try {
      const token = localStorage.getItem('token');
      const headers: Record<string, string> = {};
      if (token) headers['Authorization'] = `Bearer ${token}`;
      const res = await fetch('/api/purchase-orders', { headers });
      if (!res.ok) throw new Error(`HTTP error ${res.status}`);
      const data = await res.json();
      setPurchaseOrders(data);
    } catch (err) {
      setError('Failed to load purchase orders.');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  // Fetch truckers for assignment
  const fetchTruckers = async () => {
    try {
      const token = localStorage.getItem('token');
      const headers: Record<string, string> = {};
      if (token) headers['Authorization'] = `Bearer ${token}`;
      const res = await fetch('/api/auth/users?role=trucker', { headers });
      if (!res.ok) throw new Error('Failed to fetch truckers');
      const data = await res.json();
      setTruckers(data);
    } catch (err) {
      console.error('Failed to fetch truckers:', err);
    }
  };

  useEffect(() => {
    if (tab === 'view-pos' || tab === 'dashboard' || tab === 'assign-pickups') {
      fetchPurchaseOrders();
    }
    if (tab === 'assign-pickups') {
      fetchTruckers();
    }
  }, [tab, filter]);

  // Auto-refresh purchase orders every 30 seconds when viewing POs
  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (tab === 'view-pos' || tab === 'dashboard' || tab === 'assign-pickups') {
      interval = setInterval(() => {
        fetchPurchaseOrders();
      }, 30000); // Refresh every 30 seconds
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [tab]);

  const handleEditLoad = (po: PurchaseOrder, load: any) => {
    setEditPo(po);
    setEditLoad(load);
    setEditMaterials(load.materials ? [...load.materials] : []);
    // Convert all loads' deliveryLocation to LocationData if not already
    const safeLoads = (po.loads || []).map(l => ({
      ...l,
      deliveryLocation: typeof l.deliveryLocation === 'string' ? stringToLocationData(l.deliveryLocation) : l.deliveryLocation
    }));
    setEditLoads(JSON.parse(JSON.stringify(safeLoads)));
    setEditModalOpen(true);
  };

  const handleMaterialChange = (idx: number, field: string, value: any) => {
    setEditMaterials(mats => mats.map((m, i) => i === idx ? { ...m, [field]: value } : m));
  };

  const handleAddMaterial = () => {
    if (!editPo) return;
    // Add from available pickup materials
    const available = editPo.materials.filter(
      m => !editMaterials.some(em => em.materialId === m.id.toString())
    );
    if (available.length > 0) {
      setEditMaterials([...editMaterials, {
        materialId: available[0].id.toString(),
        materialName: available[0].name,
        quantity: 1,
        loadType: available[0].loadType || 'tons',
      }]);
    }
  };

  const handleRemoveMaterial = (idx: number) => {
    setEditMaterials(mats => mats.filter((_, i) => i !== idx));
  };

  const handleSaveMaterials = async () => {
    if (!editPo) return;
    try {
      const token = localStorage.getItem('token');
      const headers: Record<string, string> = { 'Content-Type': 'application/json' };
      if (token) headers['Authorization'] = `Bearer ${token}`;
      const res = await fetch(`/api/purchase-orders/${editPo._id}/update-loads`, {
        method: 'PUT',
        headers,
        body: JSON.stringify({ loads: editLoads }),
      });
      if (!res.ok) throw new Error('Failed to update delivery locations/materials');
      setPurchaseOrders(orders => orders.map(po =>
        po._id === editPo._id ? { ...po, loads: editLoads } : po
      ));
      setEditModalOpen(false);
    } catch (err) {
      alert('Failed to update delivery locations/materials');
    }
  };

  // After PO creation, redirect to dashboard
  const handlePoCreated = () => {
    setTab('dashboard');
  };

  const openAssignModal = (po: PurchaseOrder) => {
    setSelectedPoForAssignment(po);
    setSelectedTrucker('');
    setSelectedDeliveryLocation('');
    setSelectedMaterials([]);
    setAssignModalOpen(true);
    fetchTruckers();
  };

  const handleAssignmentMaterialChange = (materialId: string, quantity: number) => {
    setSelectedMaterials(prev => {
      const existing = prev.find(m => m.materialId === materialId);
      if (existing) {
        return prev.map(m => (m.materialId === materialId ? { materialId, quantity } : m));
      } else {
        return [...prev, { materialId, quantity }];
      }
    });
  };

  const confirmAssignment = async () => {
    if (!selectedPoForAssignment || !selectedTrucker || !selectedDeliveryLocation || selectedMaterials.length === 0) {
      alert('Please select trucker, delivery location and materials with quantities');
      return;
    }

    try {
      const token = localStorage.getItem("token");
      const headers = token ? { Authorization: `Bearer ${token}` } : {};

      const reservedMaterials = selectedMaterials.map(sel => {
        const foundMaterial = selectedPoForAssignment.materials.find(m => m.id.toString() === sel.materialId);
        return {
          materialId: sel.materialId,
          materialName: foundMaterial ? foundMaterial.name : '',
          quantity: sel.quantity,
          deliveryLocationName: selectedDeliveryLocation,
          loadType: foundMaterial ? foundMaterial.loadType : undefined,
        };
      });

      await axios.post(
        `/api/purchase-orders/${selectedPoForAssignment._id}/assign`,
        {
          deliveryLocationName: selectedDeliveryLocation,
          reservedMaterials,
          truckerId: selectedTrucker,
        },
        { headers }
      );

      setAssignModalOpen(false);
      fetchPurchaseOrders(); // Refresh the orders to show the new assignment
      alert('Trucker assigned successfully');
    } catch (error) {
      console.error('Failed to assign trucker:', error);
      alert('Failed to assign trucker');
    }
  };

  const handleDeletePO = async (poId: string) => {
    if (!window.confirm('Delete this purchase order?')) return;
    setLoading(true);
    setError(null);
    try {
      const token = localStorage.getItem('token');
      const headers: Record<string, string> = {};
      if (token) headers['Authorization'] = `Bearer ${token}`;
      const res = await fetch(`/api/purchase-orders/${poId}`, { method: 'DELETE', headers });
      if (!res.ok) throw new Error('Failed to delete purchase order');
      // Refresh the list
      fetchPurchaseOrders();
    } catch (err) {
      setError('Failed to delete purchase order');
    } finally {
      setLoading(false);
    }
  };

  const handleReopenPO = async (poId: string) => {
    if (!window.confirm('Reopen this completed purchase order?')) return;
    setLoading(true);
    setError(null);
    try {
      const token = localStorage.getItem('token');
      const headers: Record<string, string> = { 'Content-Type': 'application/json' };
      if (token) headers['Authorization'] = `Bearer ${token}`;
      const res = await fetch(`/api/purchase-orders/${poId}/reopen`, {
        method: 'POST',
        headers
      });
      if (!res.ok) throw new Error('Failed to reopen purchase order');
      // Refresh the list
      fetchPurchaseOrders();
      alert('Purchase order reopened successfully');
    } catch (err) {
      setError('Failed to reopen purchase order');
      alert('Failed to reopen purchase order');
    } finally {
      setLoading(false);
    }
  };

  // Update getDeliveredQty and all usages to only match by street (legacy) and add type guards
  const getDeliveredQty = (loadLocation: LocationData, materialId: string) => {
    if (!editPo || !editPo.transactions) return 0;
    let delivered = 0;
    editPo.transactions.forEach(tx => {
      if (tx.materialsDelivered) {
        tx.materialsDelivered.forEach(md => {
          if (
            getDeliveryLocationAddress(md.deliveryLocationName) === getDeliveryLocationAddress(loadLocation) &&
            (md.materialId?.toString?.() || md.materialId) === materialId
          ) {
            delivered += md.quantity;
          }
        });
      }
    });
    return delivered;
  };

  // Add handler to add a new delivery location
  const handleAddLoad = () => {
    setEditLoads([...editLoads, {
      deliveryLocation: {
        street: '', city: '', postalCode: '', latitude: 0, longitude: 0, state: ''
      },
      materials: []
    }]);
  };

  // Add handler to remove a delivery location (if allowed)
  const handleRemoveLoad = (idx: number) => {
    setEditLoads(editLoads.filter((_, i) => i !== idx));
  };

  // Add handler to update delivery location object
  const handleUpdateLoadLocation = (idx: number, value: LocationData) => {
    setEditLoads(editLoads.map((l, i) => i === idx ? { ...l, deliveryLocation: value } : l));
  };

  // Add handler to update materials for a load
  const handleUpdateLoadMaterials = (loadIdx: number, newMaterials: any[]) => {
    setEditLoads(editLoads.map((l, i) => i === loadIdx ? { ...l, materials: newMaterials } : l));
  };

  // When opening the edit modal, geocode any locations with lat/lon 0
  useEffect(() => {
    if (editModalOpen && editLoads.length > 0) {
      editLoads.forEach(async (load, idx) => {
        const loc = load.deliveryLocation;
        if (typeof loc === 'object' && loc.latitude === 0 && loc.street) {
          const addressString = [loc.street, loc.city, loc.state, loc.postalCode].filter(Boolean).join(', ');
          console.log('Geocoding address for delivery location:', addressString);
          const geo = await geocodeAddressToLocationData(addressString);
          if (geo.latitude && geo.longitude) {
            setEditLoads(prev => prev.map((l, i) => i === idx ? { ...l, deliveryLocation: { ...l.deliveryLocation, ...geo } } : l));
          }
        }
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [editModalOpen]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      <div className="w-full max-w-7xl mx-auto px-4 py-6 space-y-8">
        {/* Header */}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                {t('admin.adminDashboard')}
              </h1>
              <p className="text-gray-600 mt-1">Manage your operations efficiently</p>
            </div>
            <div className="flex gap-3 items-center">
              <button
                className="px-4 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg text-sm font-semibold hover:from-blue-600 hover:to-blue-700 shadow-md transition-all duration-200 transform hover:scale-105"
                onClick={() => setTab('dashboard')}
              >
                🏠 Home
              </button>
              <LanguageSwitcher />
              <button
                className="px-4 py-2 bg-gradient-to-r from-red-500 to-red-600 text-white rounded-lg text-sm font-semibold hover:from-red-600 hover:to-red-700 shadow-md transition-all duration-200 transform hover:scale-105"
                onClick={() => { localStorage.clear(); window.location.href = '/'; }}
              >
                🚪 Logout
              </button>
            </div>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-2">
          <Tabs value={tab} onValueChange={setTab}>
            <TabsList className="grid w-full grid-cols-7 bg-gray-50 rounded-xl p-1">
              <TabsTrigger value="dashboard" className="data-[state=active]:bg-white data-[state=active]:shadow-sm rounded-lg font-semibold text-sm transition-all duration-200">
                📊 Dashboard
              </TabsTrigger>
              <TabsTrigger value="clients" className="data-[state=active]:bg-white data-[state=active]:shadow-sm rounded-lg font-semibold text-sm transition-all duration-200">
                👥 Clients
              </TabsTrigger>
              <TabsTrigger value="jobs" className="data-[state=active]:bg-white data-[state=active]:shadow-sm rounded-lg font-semibold text-sm transition-all duration-200">
                💼 Jobs
              </TabsTrigger>
              <TabsTrigger value="create-po" className="data-[state=active]:bg-white data-[state=active]:shadow-sm rounded-lg font-semibold text-sm transition-all duration-200">
                📝 Create PO
              </TabsTrigger>
              <TabsTrigger value="truckers" className="data-[state=active]:bg-white data-[state=active]:shadow-sm rounded-lg font-semibold text-sm transition-all duration-200">
                🚛 Drivers
              </TabsTrigger>
              <TabsTrigger value="dispatch-orders" className="data-[state=active]:bg-white data-[state=active]:shadow-sm rounded-lg font-semibold text-sm transition-all duration-200">
                📦 Dispatch
              </TabsTrigger>
              <TabsTrigger value="warnings" className="data-[state=active]:bg-white data-[state=active]:shadow-sm rounded-lg font-semibold text-sm transition-all duration-200">
                ⚠️ Warnings
              </TabsTrigger>
            </TabsList>

            <TabsContent value="dashboard" className="space-y-6">
              {/* Dashboard summary section */}
              <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
                <h2 className="text-xl font-bold text-gray-800 mb-6">📊 Analytics Overview</h2>

                {/* Time Filter Buttons */}
                <div className="flex flex-wrap gap-3 mb-8">
                  <button
                    className={`px-6 py-3 rounded-xl font-semibold transition-all duration-200 transform hover:scale-105 ${
                      filter === 'day'
                        ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-lg'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                    onClick={() => setFilter('day')}
                  >
                    📅 Current Day
                  </button>
                  <button
                    className={`px-6 py-3 rounded-xl font-semibold transition-all duration-200 transform hover:scale-105 ${
                      filter === 'week'
                        ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-lg'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                    onClick={() => setFilter('week')}
                  >
                    📊 Week
                  </button>
                  <button
                    className={`px-6 py-3 rounded-xl font-semibold transition-all duration-200 transform hover:scale-105 ${
                      filter === 'month'
                        ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-lg'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                    onClick={() => setFilter('month')}
                  >
                    📈 Month
                  </button>
                </div>

                {/* Stats Cards */}
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-6">
                  <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl shadow-md p-6 border border-blue-200 hover:shadow-lg transition-all duration-200 transform hover:scale-105">
                    <div className="flex items-center justify-between mb-3">
                      <div className="text-3xl">🚚</div>
                      <div className="text-2xl font-bold text-blue-700">{totalPickups}</div>
                    </div>
                    <div className="text-blue-800 font-semibold">Total Pickups</div>
                  </div>

                  <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-2xl shadow-md p-6 border border-green-200 hover:shadow-lg transition-all duration-200 transform hover:scale-105">
                    <div className="flex items-center justify-between mb-3">
                      <div className="text-3xl">📦</div>
                      <div className="text-2xl font-bold text-green-700">{totalDeliveries}</div>
                    </div>
                    <div className="text-green-800 font-semibold">Total Deliveries</div>
                  </div>

                  <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-2xl shadow-md p-6 border border-yellow-200 hover:shadow-lg transition-all duration-200 transform hover:scale-105">
                    <div className="flex items-center justify-between mb-3">
                      <div className="text-3xl">⏳</div>
                      <div className="text-2xl font-bold text-yellow-700">{activeReservations}</div>
                    </div>
                    <div className="text-yellow-800 font-semibold">Active Reservations</div>
                  </div>

                  <div className="bg-gradient-to-br from-indigo-50 to-indigo-100 rounded-2xl shadow-md p-6 border border-indigo-200 hover:shadow-lg transition-all duration-200 transform hover:scale-105">
                    <div className="flex items-center justify-between mb-3">
                      <div className="text-3xl">🚛</div>
                      <div className="text-2xl font-bold text-indigo-700">{loadsInTransit}</div>
                    </div>
                    <div className="text-indigo-800 font-semibold">Loads in Transit</div>
                  </div>

                  <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-2xl shadow-md p-6 border border-purple-200 hover:shadow-lg transition-all duration-200 transform hover:scale-105">
                    <div className="flex items-center justify-between mb-3">
                      <div className="text-3xl">💰</div>
                      <div className="text-2xl font-bold text-purple-700">${dollarsEarned.toLocaleString(undefined, { maximumFractionDigits: 2 })}</div>
                    </div>
                    <div className="text-purple-800 font-semibold">Dollars Earned</div>
                  </div>
                </div>
              </div>
              {/* Purchase Orders Section */}
              <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-xl font-bold text-gray-800">📋 Purchase Orders</h2>
                  <button
                    onClick={fetchPurchaseOrders}
                    className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 transition-colors flex items-center gap-1"
                    disabled={loading}
                  >
                    <span className={loading ? 'animate-spin' : ''}>🔄</span>
                    Refresh
                  </button>
                </div>

                {/* PO Filters */}
                <div className="bg-gray-50 rounded-xl p-4 mb-6">
                  <h3 className="text-sm font-semibold text-gray-700 mb-3">🔍 Filter Orders</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div>
                      <label className="block text-xs font-semibold mb-2 text-gray-600">Client</label>
                      <input
                        type="text"
                        className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                        placeholder="Filter by client..."
                        value={poClientFilter}
                        onChange={e => setPoClientFilter(e.target.value)}
                      />
                    </div>
                    <div>
                      <label className="block text-xs font-semibold mb-2 text-gray-600">Location</label>
                      <input
                        type="text"
                        className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                        placeholder="Filter by location..."
                        value={poJobFilter}
                        onChange={e => setPoJobFilter(e.target.value)}
                      />
                    </div>
                    <div>
                      <label className="block text-xs font-semibold mb-2 text-gray-600">Job Name</label>
                      <input
                        type="text"
                        className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                        placeholder="Filter by job name..."
                        value={poJobNameFilter}
                        onChange={e => setPoJobNameFilter(e.target.value)}
                      />
                    </div>
                    <div>
                      <label className="block text-xs font-semibold mb-2 text-gray-600">PO Number</label>
                      <input
                        type="text"
                        className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                        placeholder="Filter by PO #..."
                        value={poNumberFilter}
                        onChange={e => setPoNumberFilter(e.target.value)}
                      />
                    </div>
                  </div>
                </div>
              </div>
              {/* View PO list section (copied from view-pos) */}
              <Card>
                <CardContent className="p-4">
                  <h2 className="text-lg font-semibold mb-4">{t('admin.allPOs')}</h2>
                  {loading && <p>{t('admin.loadingPOs')}</p>}
                  {error && <p className="text-red-600">{error}</p>}
                  {!loading && !error && purchaseOrders.length === 0 && (
                    <p>{t('admin.noPOsFound')}</p>
                  )}

                  {/* Filter and display logic */}
                  {(() => {
                    const filteredPOs = purchaseOrders.filter(po =>
                      (!poClientFilter || po.clientName.toLowerCase().includes(poClientFilter.toLowerCase())) &&
                      (!poJobFilter || (
                        // Match pickup location name/address
                        (po.pickupLocation && (
                          po.pickupLocation.name.toLowerCase().includes(poJobFilter.toLowerCase()) ||
                          po.pickupLocation.address.toLowerCase().includes(poJobFilter.toLowerCase())
                        )) ||
                        // Match any delivery location
                        (po.loads && po.loads.some(load => {
                          if (typeof load.deliveryLocation === 'string') {
                            return load.deliveryLocation.toLowerCase().includes(poJobFilter.toLowerCase());
                          } else if (load.deliveryLocation && typeof load.deliveryLocation === 'object' && 'street' in load.deliveryLocation) {
                            return ((load.deliveryLocation as any).street || '').toLowerCase().includes(poJobFilter.toLowerCase());
                          }
                          return false;
                        }))
                      )) &&
                      // TODO: Add job name filtering when jobName field is added to PurchaseOrder
                      // (!poJobNameFilter || (po.jobName && po.jobName.toLowerCase().includes(poJobNameFilter.toLowerCase()))) &&
                      (!poNumberFilter || po.orderNumber.toString().includes(poNumberFilter))
                    );

                    const hasFilters = poClientFilter || poJobFilter || poNumberFilter;
                    const displayPOs = hasFilters ? filteredPOs : (showAllPOs ? purchaseOrders : purchaseOrders.slice(0, 5));

                    return (
                      <>
                        {/* Show/Hide toggle for non-filtered results */}
                        {!hasFilters && purchaseOrders.length > 5 && (
                          <div className="mb-4">
                            <button
                              className="text-blue-600 hover:text-blue-800 text-sm font-semibold"
                              onClick={() => setShowAllPOs(!showAllPOs)}
                            >
                              {showAllPOs ? `Hide (showing all ${purchaseOrders.length} POs)` : `Show all ${purchaseOrders.length} POs (currently showing top 5)`}
                            </button>
                          </div>
                        )}

                        <div className="flex flex-col gap-6 max-h-[60vh] overflow-y-auto">
                          {displayPOs.map(po => (
                            <div
                              key={po._id}
                              className="relative bg-white rounded-xl shadow-lg border border-gray-200 hover:shadow-xl transition-shadow p-5 flex flex-col gap-3 group"
                            >
                              <div className="flex items-center gap-2 mb-2">
                                <span className="inline-flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 text-blue-700 text-xl font-bold">
                                  #{po.orderNumber}
                                </span>
                                <span className="ml-2 text-xs px-2 py-0.5 rounded-full font-semibold bg-gray-100 text-gray-600">
                                  {po.isCompleted ? t('dashboard.completed') : t('admin.active')}
                                </span>
                                {po.transactions && po.transactions.some(tx => tx.materialsDelivered && tx.materialsDelivered.some(dm => {
                                  const load = po.loads?.find(l => l.materials.some(m => m.materialId.toString() === (dm.materialId?.toString?.() || dm.materialId)));
                                  const origMat = load?.materials.find(m => m.materialId.toString() === (dm.materialId?.toString?.() || dm.materialId));
                                  return origMat && dm.quantity > origMat.quantity;
                                })) && (
                                  <span className="ml-2 inline-block px-2 py-0.5 bg-red-100 text-red-700 text-xs rounded font-semibold align-middle animate-pulse">{t('admin.overdelivery')}</span>
                                )}
                              </div>
                              <div className="flex flex-col gap-1 text-sm">
                                <div className="flex items-center gap-2">
                                  <span className="font-semibold text-gray-700">{t('dashboard.client')}:</span>
                                  <span className="truncate">{po.clientName}</span>
                                </div>
                                {po.jobName && (
                                  <div className="flex items-center gap-2">
                                    <span className="font-semibold text-gray-700">Job:</span>
                                    <span className="truncate">{po.jobName}</span>
                                  </div>
                                )}
                                <div className="flex items-center gap-2">
                                  <span className="font-semibold text-gray-700">{t('dashboard.pickupLocation')}:</span>
                                  <span className="truncate">{po.pickupLocation.name} ({po.pickupLocation.address})</span>
                                </div>
                                <div className="flex items-center gap-2">
                                  <span className="font-semibold text-gray-700">{t('admin.assignedTruckers')}:</span>
                                  <span className="truncate">{po.transactions && po.transactions.length > 0
                                    ? Array.from(new Set(po.transactions.map(t => {
                                      if (!t.truckerId) return '';
                                      if (typeof t.truckerId === 'object') {
                                        return t.truckerId.username || t.truckerId.email || '';
                                      }
                                      return String(t.truckerId);
                                    }))).join(', ')
                                    : t('admin.unassigned')}
                                  </span>
                                </div>
                                <div className="flex items-center gap-2">
                                  <span className="font-semibold text-gray-700">Materials Picked Up:</span>
                                  <span>
                                    {po.materials && po.materials.length > 0 ? (
                                      <ul className="ml-2 space-y-0.5">
                                        {po.materials.map((mat, idx) => (
                                          <li key={mat.id || idx} className="text-xs text-gray-700">
                                            <span className="font-semibold">{mat.name}</span> — Qty: {mat.quantity} {mat.loadType}
                                          </li>
                                        ))}
                                      </ul>
                                    ) : (
                                      <span className="ml-2 text-gray-500">No pickup materials</span>
                                    )}
                                  </span>
                                </div>
                                <div className="flex items-center gap-2">
                                  <span className="font-semibold text-gray-700">{t('dashboard.deliveryLocationsMaterials')}</span>
                                  <span>
                                    {po.loads && po.loads.length > 0 ? (
                                      <ul className="ml-2 space-y-0.5">
                                        {po.loads.map((load, idx) => (
                                          <li key={idx} className="flex items-center gap-1 text-xs text-gray-700">
                                            <span className="text-blue-700">📍 {getDeliveryLocationAddress(load.deliveryLocation)}</span>
                                            <span className="ml-1">{load.materials.map((mat, midx) => (
                                              <span key={midx} className="inline-block mr-2">
                                                <span className="font-semibold">{mat.materialName}</span> ({mat.quantity} {mat.loadType})
                                              </span>
                                            ))}</span>
                                          </li>
                                        ))}
                                      </ul>
                                    ) : (
                                      <span className="ml-2 text-gray-500">{t('admin.noDeliveryLocations')}</span>
                                    )}
                                  </span>
                                </div>
                              </div>

                              {/* Delivery Locations & Materials */}
                              <div className="mt-3">
                                <h4 className="font-medium text-gray-800 mb-2">Delivery Locations & Materials:</h4>
                                {po.loads && po.loads.length > 0 ? (
                                  <div className="space-y-2">
                                    {po.loads.map((load, idx) => (
                                      <div key={idx} className="bg-gray-50 p-3 rounded">
                                        <h5 className="font-medium text-blue-700">📍 {getDeliveryLocationAddress(load.deliveryLocation)}</h5>
                                        <ul className="ml-4 mt-1">
                                          {load.materials.map((mat, midx) => (
                                            <li key={midx} className="text-sm text-gray-700">
                                              <span className="font-semibold">{mat.materialName}</span> —
                                              Qty: {mat.quantity} {mat.loadType}
                                              {mat.remainingQuantity !== undefined && (
                                                <span className="text-blue-600"> (Remaining: {mat.remainingQuantity})</span>
                                              )}
                                            </li>
                                          ))}
                                        </ul>
                                      </div>
                                    ))}
                                  </div>
                                ) : (
                                  <p className="text-gray-500 text-sm">No delivery locations specified</p>
                                )}
                              </div>

                              <div className="flex gap-2 mt-2">
                                <button
                                  className="px-3 py-1 bg-blue-100 text-blue-800 rounded text-xs font-semibold hover:bg-blue-200 transition"
                                  onClick={() => setExpandedTransactions(prev => ({ ...prev, [po._id]: !prev[po._id] }))}
                                >
                                  {expandedTransactions[po._id] ? t('admin.hideTransactions') || 'Hide Transactions' : t('admin.viewTransactions') || 'View Transactions'}
                                </button>
                                <button
                                  className="px-3 py-1 bg-yellow-100 text-yellow-800 rounded text-xs font-semibold hover:bg-yellow-200 transition"
                                  onClick={() => {
                                    setEditPo(po);
                                    // Convert all loads' deliveryLocation to LocationData if not already
                                    const safeLoads = (po.loads || []).map(l => ({
                                      ...l,
                                      deliveryLocation: typeof l.deliveryLocation === 'string' ? stringToLocationData(l.deliveryLocation) : l.deliveryLocation
                                    }));
                                    setEditLoads(JSON.parse(JSON.stringify(safeLoads)));
                                    setEditModalOpen(true);
                                  }}
                                >
                                  Edit
                                </button>
                                {po.isCompleted && (
                                  <button
                                    className="px-2 py-1 bg-green-100 text-green-800 rounded text-xs font-semibold hover:bg-green-200 transition"
                                    onClick={() => handleReopenPO(po._id)}
                                  >
                                    Reopen
                                  </button>
                                )}
                                <button
                                  className="px-2 py-1 bg-red-100 text-red-800 rounded text-xs font-semibold hover:bg-red-200 transition"
                                  onClick={() => handleDeletePO(po._id)}
                                >
                                  Delete
                                </button>
                              </div>
                              {expandedTransactions[po._id] && (
                                <div className="mt-3 border-t pt-3">
                                  <span className="font-semibold">{t('admin.transactions')}:</span>
                                  {po.transactions && po.transactions.length > 0 ? (
                                    <ul className="ml-4 mt-2 space-y-2">
                                      {po.transactions.map((tx, txIdx) => (
                                        <li key={tx._id || txIdx} className="mb-2 border-b pb-2">
                                          <div className="font-medium text-blue-800">{t('admin.trucker')}: {tx.truckerId && (typeof tx.truckerId === 'object' ? (tx.truckerId.username || tx.truckerId.email || '') : String(tx.truckerId))}</div>
                                          <div>{t('admin.status')}: <span className="font-semibold">{tx.status}</span></div>
                                          <div>{t('admin.reservedAt')}: {tx.reservedAt ? new Date(tx.reservedAt).toLocaleString() : 'N/A'}</div>
                                          {tx.pickedUpAt && <div>{t('admin.pickedUpAt')}: {new Date(tx.pickedUpAt).toLocaleString()}</div>}
                                          {tx.completedAt && <div>{t('admin.completedAt')}: {new Date(tx.completedAt).toLocaleString()}</div>}
                                          <div className="mt-1">
                                            <span className="font-semibold">{t('admin.reservedMaterials')}:</span>
                                            <ul className="ml-4">
                                              {tx.reservedMaterials && tx.reservedMaterials.map((rm, rIdx) => (
                                                <li key={rIdx}>{rm.materialName} — {t('dashboard.qty')}: {rm.quantity} @ {rm.deliveryLocationName}</li>
                                              ))}
                                            </ul>
                                          </div>
                                          {tx.materialsPickedUp && (
                                            <div className="mt-1">
                                              <span className="font-semibold">{t('admin.pickedUpMaterials')}:</span>
                                              <ul className="ml-4">
                                                {tx.materialsPickedUp.map((pm, pIdx) => (
                                                  <li key={pIdx}>{pm.materialName || pm.materialId} — {t('dashboard.qty')}: {pm.quantity} @ {pm.deliveryLocationName}</li>
                                                ))}
                                              </ul>
                                            </div>
                                          )}
                                          {tx.materialsDelivered && (
                                            <div className="mt-1">
                                              <span className="font-semibold">{t('admin.deliveredMaterials')}:</span>
                                              <ul className="ml-4">
                                                {tx.materialsDelivered.map((dm, dIdx) => (
                                                  <li key={dIdx}>{dm.materialName || dm.materialId} — {t('dashboard.qty')}: {dm.quantity} @ {dm.deliveryLocationName}</li>
                                                ))}
                                              </ul>
                                            </div>
                                          )}
                                          {/* Signatures and Photos */}
                                          {(tx.signature || tx.pickupTicketImage || tx.deliverySignature || tx.deliveryTicketImage) && (
                                            <div className="mt-2 p-2 bg-gray-50 rounded">
                                              <span className="font-semibold text-sm">Documents & Signatures:</span>
                                              <div className="flex flex-wrap gap-2 mt-1">
                                                {tx.signature && (
                                                  <button
                                                    className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200"
                                                    onClick={() => {
                                                      setModalImage(`data:image/png;base64,${tx.signature}`);
                                                      setModalTitle('Pickup Signature');
                                                      setModalOpen(true);
                                                    }}
                                                  >
                                                    📝 Pickup Signature
                                                  </button>
                                                )}
                                                {tx.pickupTicketImage && (
                                                  <button
                                                    className="px-2 py-1 bg-green-100 text-green-800 rounded text-xs hover:bg-green-200"
                                                    onClick={() => {
                                                      setModalImage(`data:image/jpeg;base64,${tx.pickupTicketImage}`);
                                                      setModalTitle('Pickup Ticket');
                                                      setModalOpen(true);
                                                    }}
                                                  >
                                                    📄 Pickup Ticket
                                                  </button>
                                                )}
                                                {tx.deliverySignature && (
                                                  <button
                                                    className="px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs hover:bg-purple-200"
                                                    onClick={() => {
                                                      setModalImage(`data:image/png;base64,${tx.deliverySignature}`);
                                                      setModalTitle('Delivery Signature');
                                                      setModalOpen(true);
                                                    }}
                                                  >
                                                    📝 Delivery Signature
                                                  </button>
                                                )}
                                                {tx.deliveryTicketImage && (
                                                  <button
                                                    className="px-2 py-1 bg-orange-100 text-orange-800 rounded text-xs hover:bg-orange-200"
                                                    onClick={() => {
                                                      setModalImage(`data:image/jpeg;base64,${tx.deliveryTicketImage}`);
                                                      setModalTitle('Delivery Ticket');
                                                      setModalOpen(true);
                                                    }}
                                                  >
                                                    📄 Delivery Ticket
                                                  </button>
                                                )}
                                              </div>
                                            </div>
                                          )}
                                        </li>
                                      ))}
                                    </ul>
                                  ) : (
                                    <span className="ml-2 text-gray-500">{t('admin.noTransactions')}</span>
                                  )}
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      </>
                    );
                  })()}
                </CardContent>
              </Card>
            </TabsContent>

        <TabsContent value="clients">
          <ClientManagement />
        </TabsContent>

        <TabsContent value="jobs">
          <JobManagement />
        </TabsContent>

        <TabsContent value="create-po">
          <Card>
            <CardContent className="p-4">
              <h2 className="text-lg font-semibold mb-2">{t('admin.createNewPO')}</h2>
              <PoCreate onCreated={handlePoCreated} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="truckers">
          <TruckerOrganization />
        </TabsContent>

        <TabsContent value="dispatch-orders">
          <Card>
            <CardContent className="p-4">
              <h2 className="text-lg font-semibold mb-4">Dispatch Orders</h2>
              {loading && <p>Loading purchase orders...</p>}
              {error && <p className="text-red-600">{error}</p>}
              {!loading && !error && purchaseOrders.length === 0 && (
                <p>No purchase orders found</p>
              )}
              <div className="space-y-4">
                {purchaseOrders
                  .filter(po => !po.isCompleted) // Only show uncompleted orders
                  .map(po => (
                    <div key={po._id} className="border rounded-lg p-4 bg-white">
                      <div className="flex justify-between items-start mb-3">
                        <div>
                          <h3 className="font-semibold text-lg">Order #{po.orderNumber}</h3>
                          <p className="text-gray-600">Client: {po.clientName}</p>
                          {po.jobName && <p className="text-gray-600">Job: {po.jobName}</p>}
                          <p className="text-gray-600">Pickup: {po.pickupLocation.name}</p>
                        </div>
                        <div className="text-right">
                          <p className="text-sm text-gray-500">
                            Assigned Truckers: {po.transactions && po.transactions.length > 0
                              ? Array.from(new Set(po.transactions.map(t => {
                                if (!t.truckerId) return '';
                                if (typeof t.truckerId === 'object') {
                                  return t.truckerId.username || t.truckerId.email || '';
                                }
                                return String(t.truckerId);
                              }))).join(', ')
                              : 'None'
                            }
                          </p>
                          <button
                            className="mt-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                            onClick={() => openAssignModal(po)}
                          >
                            Assign Trucker
                          </button>
                        </div>
                      </div>
                      <div className="mb-3">
                        <h4 className="font-medium mb-2">Delivery Locations & Materials:</h4>
                        {po.loads && po.loads.length > 0 ? (
                          <div className="space-y-2">
                            {po.loads.map((load, idx) => (
                              <div key={idx} className="bg-gray-50 p-3 rounded">
                                <h5 className="font-medium text-blue-700">📍 {getDeliveryLocationAddress(load.deliveryLocation)}</h5>
                                <ul className="ml-4 mt-1">
                                  {load.materials.map((mat, midx) => (
                                    <li key={midx} className="text-sm text-gray-700">
                                      <span className="font-semibold">{mat.materialName}</span> — 
                                      Quantity: {mat.quantity} {mat.loadType}
                                      {mat.remainingQuantity !== undefined && (
                                        <span className="text-blue-600 ml-2">
                                          (Remaining: {mat.remainingQuantity})
                                        </span>
                                      )}
                                    </li>
                                  ))}
                                </ul>
                              </div>
                            ))}
                          </div>
                        ) : (
                          <p className="text-gray-500">No delivery locations assigned</p>
                        )}
                      </div>
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="warnings">
          <Card>
            <CardContent className="p-4">
              <h2 className="text-lg font-semibold mb-2">{t('admin.overdeliveryWarnings')}</h2>
              <table className="w-full text-sm border-collapse">
                <thead>
                  <tr className="bg-red-50">
                    <th className="p-2 border-b text-left">PO #</th>
                    <th className="p-2 border-b text-left">Material</th>
                    <th className="p-2 border-b text-left">Location</th>
                    <th className="p-2 border-b text-left">Trucker</th>
                    <th className="p-2 border-b text-left">Requested</th>
                    <th className="p-2 border-b text-left">Delivered</th>
                    <th className="p-2 border-b text-left">Overage</th>
                  </tr>
                </thead>
                <tbody>
                  {purchaseOrders.flatMap(po =>
                    (po.transactions || []).flatMap(tx =>
                      (tx.materialsDelivered || []).map(dm => {
                        const load = po.loads?.find(l => l.materials.some(m => m.materialId.toString() === (dm.materialId?.toString?.() || dm.materialId)));
                        const origMat = load?.materials.find(m => m.materialId.toString() === (dm.materialId?.toString?.() || dm.materialId));
                        const overage = origMat ? dm.quantity - origMat.quantity : 0;
                        if (origMat && overage > 0) {
                          return {
                            po,
                            tx,
                            dm,
                            load,
                            origMat,
                            overage
                          };
                        }
                        return null;
                      }).filter(Boolean)
                    )
                  ).filter(Boolean).length === 0 ? (
                    <tr><td colSpan={7} className="p-4 text-center text-gray-500">No overdelivery warnings.</td></tr>
                  ) : (
                    purchaseOrders.flatMap(po =>
                      (po.transactions || []).flatMap(tx =>
                        (tx.materialsDelivered || []).map(dm => {
                          const load = po.loads?.find(l => l.materials.some(m => m.materialId.toString() === (dm.materialId?.toString?.() || dm.materialId)));
                          const origMat = load?.materials.find(m => m.materialId.toString() === (dm.materialId?.toString?.() || dm.materialId));
                          const overage = origMat ? dm.quantity - origMat.quantity : 0;
                          if (origMat && overage > 0) {
                            return (
                              <tr key={`${po._id}-${tx._id}-${dm.materialId}-${dm.deliveryLocationName}`} className="bg-red-50">
                                <td className="p-2 border-b">{po.orderNumber}</td>
                                <td className="p-2 border-b">{dm.materialName || origMat.materialName}</td>
                                <td className="p-2 border-b">{dm.deliveryLocationName}</td>
                                <td className="p-2 border-b">{tx.truckerId && (typeof tx.truckerId === 'object' ? (tx.truckerId.username || tx.truckerId.email || '') : String(tx.truckerId))}</td>
                                <td className="p-2 border-b">{origMat.quantity}</td>
                                <td className="p-2 border-b">{dm.quantity}</td>
                                <td className="p-2 border-b font-bold text-red-700">+{overage}</td>
                              </tr>
                            );
                          }
                          return null;
                        }).filter(Boolean)
                      )
                    ).filter(Boolean)
                  )}
                </tbody>
              </table>
            </CardContent>
          </Card>
        </TabsContent>
          </Tabs>
        </div>

        {modalOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4">
          <div className="bg-white p-4 md:p-6 rounded-xl shadow-lg w-full max-w-md max-h-[90vh] overflow-auto">
            <h3 className="text-lg md:text-xl font-semibold mb-4 text-[var(--text-primary)]">{modalTitle}</h3>
            <div className="flex justify-center mb-4 min-h-[100px]">
              {loadingImage ? (
                <div className="w-full flex justify-center items-center h-32">
                  <span className="loader" />
                          </div>
              ) : (
                modalImage && <img src={modalImage} alt={modalTitle} className="max-w-full max-h-64 border border-gray-300 rounded" />
                            )}
                          </div>
            <div className="flex justify-end">
              <button
                className="px-4 py-2 rounded-md bg-gray-200 text-gray-800 font-medium hover:bg-gray-300"
                onClick={() => setModalOpen(false)}
              >
                Close
              </button>
                      </div>
                                    </div>
                                      </div>
                                    )}

      {editModalOpen && editPo && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4">
          <div className="bg-white p-4 md:p-6 rounded-xl shadow-lg w-full max-w-2xl max-h-[90vh] overflow-auto">
            <h3 className="text-lg md:text-xl font-semibold mb-4">Edit Delivery Locations & Materials</h3>
            {/* PO summary for context */}
            <div className="mb-4 p-3 bg-gray-50 rounded border">
              <div className="mb-1"><span className="font-semibold">PO #:</span> {editPo.orderNumber}</div>
              <div className="mb-1"><span className="font-semibold">Client:</span> {editPo.clientName}</div>
              <div className="mb-1"><span className="font-semibold">Pickup Location:</span> {editPo.pickupLocation?.name} ({editPo.pickupLocation?.address})</div>
              <div className="mb-1"><span className="font-semibold">Materials:</span> {editPo.materials.map(m => `${m.name} (${m.quantity} ${m.loadType})`).join(', ')}</div>
            </div>
            <div className="space-y-6 mb-4">
              {editLoads.map((load, loadIdx) => {
                // Check if any material has been delivered to this location
                const deliveredForLoad = (editPo.transactions || []).some(tx =>
                  (tx.materialsDelivered || []).some(md =>
                    isLocationData(load.deliveryLocation) &&
                    getDeliveryLocationAddress(md.deliveryLocationName) === getDeliveryLocationAddress(load.deliveryLocation) &&
                    md.quantity > 0
                  )
                );
                // Check if any trucker is assigned to this location (any transaction for this location)
                const assignedTruckerForLoad = (editPo.transactions || []).some(tx =>
                  (tx.reservedMaterials || []).some(rm =>
                    isLocationData(load.deliveryLocation) &&
                    rm.deliveryLocationName === getDeliveryLocationAddress(load.deliveryLocation)
                  )
                );
                const cannotDelete = deliveredForLoad || assignedTruckerForLoad;
                let deleteTitle = 'Delete location';
                if (deliveredForLoad && assignedTruckerForLoad) deleteTitle = 'Cannot delete: materials delivered and trucker assigned';
                else if (deliveredForLoad) deleteTitle = 'Cannot delete: materials already delivered';
                else if (assignedTruckerForLoad) deleteTitle = 'Cannot delete: trucker assigned';
                const totalAssignedQty = load.materials.reduce((sum: number, m: any) => sum + (Number(m.quantity) || 0), 0);
                return (
                  <div key={loadIdx} className="border rounded p-3 mb-2">
                    <div className="flex items-center gap-2 mb-2">
                      <LocationInput
                        location={isLocationData(load.deliveryLocation) ? load.deliveryLocation : stringToLocationData(load.deliveryLocation)}
                        onChange={loc => handleUpdateLoadLocation(loadIdx, loc)}
                        label={`Delivery Location ${loadIdx + 1}`}
                        placeholder="Search for delivery location..."
                      />
                      <button
                        className="px-2 py-1 bg-red-100 text-red-800 rounded text-xs font-semibold hover:bg-red-200 disabled:opacity-50"
                        onClick={() => handleRemoveLoad(loadIdx)}
                        disabled={cannotDelete}
                        title={deleteTitle}
                      >Delete</button>
                    </div>
                    {/* Materials for this load */}
                    <div className="space-y-2">
                      {editPo.materials.map((mat, matIdx) => {
                        // Always compare materialId as string for robustness
                        const assigned = load.materials.find((m: any) => String(m.materialId) === String(mat.id));
                        const assignedQty = assigned ? assigned.quantity : 0;
                        const deliveredQty = isLocationData(load.deliveryLocation) ? getDeliveredQty(load.deliveryLocation, String(mat.id)) : 0;
                        return (
                          <div key={mat.id} className="flex items-center gap-2">
                            <span className="font-semibold text-sm md:text-base">{mat.name}</span>
                            <input
                              type="number"
                              min={deliveredQty}
                              value={assignedQty}
                              onChange={e => {
                                let val = Number(e.target.value);
                                if (val < deliveredQty) val = deliveredQty;
                                let newMaterials = [...load.materials];
                                if (val <= 0) {
                                  // Remove material assignment
                                  newMaterials = newMaterials.filter((m: any) => String(m.materialId) !== String(mat.id));
                                } else if (assigned) {
                                  // Update existing assignment
                                  newMaterials = newMaterials.map((m: any) =>
                                    String(m.materialId) === String(mat.id)
                                      ? { ...m, quantity: val }
                                      : m
                                  );
                                } else {
                                  // Add new assignment
                                  newMaterials.push({
                                    materialId: String(mat.id),
                                    materialName: mat.name,
                                    quantity: val,
                                    loadType: mat.loadType || 'tons',
                                  });
                                }
                                handleUpdateLoadMaterials(loadIdx, newMaterials);
                              }}
                              className="w-20 border rounded p-1"
                            />
                            <span className="text-xs text-gray-500">{mat.loadType}</span>
                            {deliveredQty > 0 && (
                              <span className="text-xs text-blue-600 ml-2">(Delivered: {deliveredQty})</span>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  </div>
                );
              })}
              <button
                className="px-4 py-2 rounded-md bg-green-600 text-white font-medium hover:bg-green-700"
                onClick={handleAddLoad}
              >Add Delivery Location</button>
            </div>
            <div className="flex justify-end gap-2">
              <button className="px-4 py-2 rounded-md bg-gray-200 text-gray-800 font-medium hover:bg-gray-300" onClick={() => setEditModalOpen(false)}>{t('admin.cancel')}</button>
              <button className="px-4 py-2 rounded-md bg-blue-600 text-white font-medium hover:bg-blue-700" onClick={handleSaveMaterials}>{t('admin.save')}</button>
            </div>
          </div>
        </div>
      )}

      {/* Assignment Modal */}
      {assignModalOpen && selectedPoForAssignment && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4">
          <div className="bg-white p-6 md:p-8 rounded-2xl shadow-2xl w-full max-w-lg max-h-[90vh] overflow-auto border border-gray-200">
            <div className="flex items-center gap-3 mb-6">
              <div className="text-3xl">🚛</div>
              <h3 className="text-xl md:text-2xl font-bold text-gray-800">
                Assign Trucker to Order #{selectedPoForAssignment.orderNumber}
              </h3>
            </div>

            <div className="mb-6">
              <label className="block mb-2 font-semibold text-gray-700 flex items-center gap-2">
                👤 Select Trucker
              </label>
              <select
                value={selectedTrucker}
                onChange={e => setSelectedTrucker(e.target.value)}
                className="w-full border border-gray-300 rounded-xl p-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 shadow-sm"
              >
                <option value="">Choose a trucker...</option>
                {truckers.map(trucker => (
                  <option key={trucker._id} value={trucker._id}>
                    {trucker.username} ({trucker.email})
                  </option>
                ))}
              </select>
            </div>

            <div className="mb-6">
              <label className="block mb-2 font-semibold text-gray-700 flex items-center gap-2">
                📍 Delivery Location
              </label>
              <select
                value={selectedDeliveryLocation}
                onChange={e => setSelectedDeliveryLocation(e.target.value)}
                className="w-full border border-gray-300 rounded-xl p-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 shadow-sm"
              >
                <option value="">Select delivery location</option>
                {selectedPoForAssignment.loads && selectedPoForAssignment.loads.map((load, idx) => {
                  let displayLocation = '';
                  let locationKey = '';

                  displayLocation = getDeliveryLocationAddress(load.deliveryLocation);
                  if (isLocationData(load.deliveryLocation)) {
                    const loc = load.deliveryLocation;
                    locationKey = loc.street || displayLocation; // Use street as primary key, fallback to full address
                  } else if (typeof load.deliveryLocation === 'string') {
                    locationKey = load.deliveryLocation;
                  } else {
                    locationKey = `location_${idx}`;
                  }

                  return (
                    <option key={locationKey || idx} value={locationKey}>
                      {displayLocation || '(No address)'}
                    </option>
                  );
                })}
              </select>
            </div>

            {selectedDeliveryLocation ? (
              (() => {
                // Find the load by matching the location key
                const load = selectedPoForAssignment.loads?.find(l => {
                  if (isLocationData(l.deliveryLocation)) {
                    const loc = l.deliveryLocation;
                    const locationKey = loc.street || getDeliveryLocationAddress(l.deliveryLocation);
                    return locationKey === selectedDeliveryLocation;
                  } else if (typeof l.deliveryLocation === 'string') {
                    return l.deliveryLocation === selectedDeliveryLocation;
                  }
                  return false;
                });
                if (!load || !load.materials.length) {
                  return <p className="text-sm text-red-500 mb-4">No materials available for this location.</p>;
                }
                return (
                  <div className="mb-4">
                    <p className="font-medium mb-2">Select materials and quantities:</p>
                    {load.materials.map(mat => {
                      const selectedMat = selectedMaterials.find(m => m.materialId === mat.materialId.toString());
                      return (
                        <div key={mat.materialId} className="flex flex-col md:flex-row items-start md:items-center gap-3 mb-3 p-2 rounded hover:bg-gray-50 border">
                          <div className="flex items-center gap-2">
                            <input
                              type="checkbox"
                              checked={!!selectedMat}
                              onChange={e => {
                                if (e.target.checked) {
                                  handleAssignmentMaterialChange(mat.materialId.toString(), 1);
                                        } else {
                                  setSelectedMaterials(prev => prev.filter(m => m.materialId !== mat.materialId.toString()));
                                }
                              }}
                              className="accent-blue-600 w-4 h-4"
                            />
                            <span className="font-semibold text-[var(--text-primary)] text-sm md:text-base">{mat.materialName}</span>
                            <span className="text-xs text-gray-500">{mat.loadType}</span>
                                    </div>
                          {selectedMat && (
                            <div className="flex items-center gap-2 w-full md:w-auto">
                              <input
                                type="number"
                                min={1}
                                max={mat.remainingQuantity || mat.quantity}
                                value={selectedMat.quantity}
                                onChange={e => handleAssignmentMaterialChange(mat.materialId.toString(), Number(e.target.value))}
                                className="w-full md:w-20 border border-gray-300 rounded p-1"
                              />
                              <span className="text-xs text-gray-500">
                                / {mat.remainingQuantity ?? mat.quantity} max
                              </span>
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                );
              })()
            ) : (
              <p className="text-sm text-gray-500 mb-4">Please select a delivery location to see available materials.</p>
            )}

            <div className="flex justify-end gap-4 mt-8 pt-4 border-t border-gray-200">
              <button
                className="px-6 py-3 rounded-xl bg-gray-100 text-gray-700 font-semibold hover:bg-gray-200 transition-all duration-200 transform hover:scale-105"
                onClick={() => setAssignModalOpen(false)}
              >
                ❌ Cancel
              </button>
              <button
                className="px-6 py-3 rounded-xl bg-gradient-to-r from-blue-500 to-blue-600 text-white font-semibold hover:from-blue-600 hover:to-blue-700 shadow-md transition-all duration-200 transform hover:scale-105"
                onClick={confirmAssignment}
              >
                ✅ Assign Trucker
              </button>
            </div>
          </div>
        </div>
      )}

        {modalOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4">
          <div className="bg-white p-4 md:p-6 rounded-xl shadow-lg w-full max-w-md max-h-[90vh] overflow-auto">
            <h3 className="text-lg md:text-xl font-semibold mb-4 text-[var(--text-primary)]">{modalTitle}</h3>
            <div className="flex justify-center mb-4 min-h-[100px]">
              {modalImage ? (
                <img
                  src={modalImage}
                  alt={modalTitle}
                  className="max-w-full max-h-[60vh] object-contain rounded shadow-lg"
                />
              ) : (
                <div className="flex items-center justify-center text-gray-500">
                  Loading...
                </div>
              )}
            </div>
            <div className="flex justify-end">
              <button
                className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
                onClick={() => {
                  setModalOpen(false);
                  setModalImage(null);
                  setModalTitle('');
                }}
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
      </div>
    </div>
  );
};

export default AdminDashboard;
