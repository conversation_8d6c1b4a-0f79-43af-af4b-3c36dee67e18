import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

const resources = {
  en: {
    translation: {
      login: {
        welcome: 'Welcome to Trucking Logistics',
        login: 'Sign in to manage your logistics operations',
        access: 'Sign in to manage your logistics operations',
        username: '<PERSON><PERSON><PERSON>',
        password: 'Password',
        button: 'Sign in',
        error: 'Invalid username or password.',
        usernamePlaceholder: 'Userna<PERSON>',
        passwordPlaceholder: 'Password'
      },
      dashboard: {
        availableOrders: 'Available Orders',
        reservePickup: 'Reserve Pickup',
        deliveryLocation: 'Delivery Location',
        selectDeliveryLocation: 'Select delivery location',
        selectMaterials: 'Select materials and quantities:',
        noMaterials: 'No materials available for this location.',
        pleaseSelectLocation: 'Please select a delivery location to see available materials.',
        confirm: 'Confirm Reservation',
        cancel: 'Cancel',
        totalValue: 'Total Value',
        client: 'Client',
        pickupLocation: 'Pickup Location',
        totalRequired: 'Total Required',
        remaining: 'Remaining',
        summary: 'Summary',
        completedOrders: 'Completed Orders',
        transactionHistory: 'Transaction History',
        showLess: 'Show Less',
        viewAll: 'View All',
        logout: 'Logout',
        dashboard: 'Dashboard',
        orders: 'Orders',
        profile: 'Profile',
        noAvailableOrders: 'No available orders at the moment.',
        completedOrder: '✓ This PO has been completed',
        orderNumber: 'Order #{{number}}',
        deliveryLocationsMaterials: 'Delivery Locations & Materials:',
        noCompletedTransactions: 'No completed transactions yet.',
        completed: 'Completed',
        transactionId: 'Transaction ID',
        materialsDelivered: 'Materials Delivered:',
        qty: 'Qty',
        viewPickupSignature: 'View Pickup Signature',
        viewPickupTicket: 'View Pickup Ticket',
        viewDeliverySignature: 'View Delivery Signature',
        viewDeliveryTicket: 'View Delivery Ticket',
        totalOrders: 'Total Orders'
      },
      admin: {
        adminDashboard: 'Admin Dashboard',
        createPO: 'Create PO',
        viewPOs: 'View POs',
        trackProgress: 'Track Progress',
        warnings: 'Warnings',
        createNewPO: 'Create New Purchase Order',
        allPOs: 'All Purchase Orders',
        loadingPOs: 'Loading purchase orders...',
        noPOsFound: 'No purchase orders found.',
        orderNumber: 'Order #{{number}}',
        active: 'Active',
        overdelivery: 'Overdelivery!',
        created: 'Created',
        assignedTruckers: 'Assigned Truckers',
        unassigned: 'Unassigned',
        noDeliveryLocations: 'No delivery locations',
        transactions: 'Transactions',
        trucker: 'Trucker',
        status: 'Status',
        reservedAt: 'Reserved At',
        pickedUpAt: 'Picked Up At',
        completedAt: 'Completed At',
        reservedMaterials: 'Reserved Materials',
        pickedUpMaterials: 'Pickup Materials',
        deliveredMaterials: 'Delivered Materials',
        noTransactions: 'No transactions yet.',
        deliveryProgressTracker: 'Delivery Progress Tracker',
        progressTrackingUI: 'Progress tracking UI goes here...',
        overdeliveryWarnings: 'Overdelivery Warnings',
        warning: 'Warning',
        materialMismatchWarning: '{{material}} assigned: {{assigned}} {{loadType}}, picked up: {{pickedup}} {{loadType}}',
        viewTransactions: 'View Transactions',
        hideTransactions: 'Hide Transactions',
      }
    }
  },
  es: {
    translation: {
      login: {
        welcome: 'Bienvenido a Trucking Logistics',
        login: 'Inicia sesión para gestionar tus operaciones logísticas',
        access: 'Inicia sesión para gestionar tus operaciones logísticas',
        username: 'Usuario',
        password: 'Contraseña',
        button: 'Iniciar sesión',
        error: 'Usuario o contraseña inválidos.',
        usernamePlaceholder: 'Usuario',
        passwordPlaceholder: 'Contraseña'
      },
      dashboard: {
        availableOrders: 'Órdenes Disponibles',
        reservePickup: 'Reservar Recogida',
        deliveryLocation: 'Lugar de Entrega',
        selectDeliveryLocation: 'Selecciona lugar de entrega',
        selectMaterials: 'Selecciona materiales y cantidades:',
        noMaterials: 'No hay materiales disponibles para este lugar.',
        pleaseSelectLocation: 'Por favor selecciona un lugar de entrega para ver los materiales disponibles.',
        confirm: 'Confirmar Reserva',
        cancel: 'Cancelar',
        totalValue: 'Valor Total',
        client: 'Cliente',
        pickupLocation: 'Lugar de Recogida',
        totalRequired: 'Total Requerido',
        remaining: 'Restante',
        summary: 'Resumen',
        completedOrders: 'Órdenes Completadas',
        transactionHistory: 'Historial de Transacciones',
        showLess: 'Mostrar Menos',
        viewAll: 'Ver Todo',
        logout: 'Cerrar sesión',
        dashboard: 'Panel',
        orders: 'Órdenes',
        profile: 'Perfil',
        noAvailableOrders: 'No hay órdenes disponibles en este momento.',
        completedOrder: '✓ Esta orden ha sido completada',
        orderNumber: 'Orden #{{number}}',
        deliveryLocationsMaterials: 'Lugares de Entrega y Materiales:',
        noCompletedTransactions: 'No hay transacciones completadas aún.',
        completed: 'Completado',
        transactionId: 'ID de Transacción',
        materialsDelivered: 'Materiales Entregados:',
        qty: 'Cant.',
        viewPickupSignature: 'Ver Firma de Recogida',
        viewPickupTicket: 'Ver Ticket de Recogida',
        viewDeliverySignature: 'Ver Firma de Entrega',
        viewDeliveryTicket: 'Ver Ticket de Entrega',
        totalOrders: 'Órdenes Totales'
      },
      admin: {
        adminDashboard: 'Panel de Administrador',
        createPO: 'Crear PO',
        viewPOs: 'Ver POs',
        trackProgress: 'Seguimiento',
        warnings: 'Advertencias',
        createNewPO: 'Crear Nueva Orden de Compra',
        allPOs: 'Todas las Órdenes de Compra',
        loadingPOs: 'Cargando órdenes de compra...',
        noPOsFound: 'No se encontraron órdenes de compra.',
        orderNumber: 'Orden #{{number}}',
        active: 'Activa',
        overdelivery: '¡Sobreentrega!',
        created: 'Creada',
        assignedTruckers: 'Camioneros Asignados',
        unassigned: 'Sin asignar',
        noDeliveryLocations: 'Sin lugares de entrega',
        transactions: 'Transacciones',
        trucker: 'Camionero',
        status: 'Estado',
        reservedAt: 'Reservada el',
        pickedUpAt: 'Recogida el',
        completedAt: 'Completada el',
        reservedMaterials: 'Materiales Reservados',
        pickedUpMaterials: 'Materiales de Recogida',
        deliveredMaterials: 'Materiales Entregados',
        noTransactions: 'Sin transacciones aún.',
        deliveryProgressTracker: 'Seguimiento de Entregas',
        progressTrackingUI: 'La interfaz de seguimiento de progreso va aquí...',
        overdeliveryWarnings: 'Advertencias de Sobreentrega',
        warning: 'Advertencia',
        materialMismatchWarning: '{{material}} asignado: {{assigned}} {{loadType}}, recogido: {{pickedup}} {{loadType}}',
        viewTransactions: 'Ver Transacciones',
        hideTransactions: 'Ocultar Transacciones',
      }
    }
  }
};

i18n
  .use(initReactI18next)
  .init({
    resources,
    lng: 'en',
    fallbackLng: 'en',
    interpolation: {
      escapeValue: false,
    },
  });

export default i18n; 