import React, { useEffect, useState } from 'react';
import LocationInput from './LocationInput';

interface Client {
  _id?: string;
  name: string;
}

type LocationData = {
  street: string;
  city: string;
  state?: string;
  postalCode: string;
  latitude: number;
  longitude: number;
  address1?: string;
  zipCode?: string;
};

type BackendLocationData = {
  address1: string;
  city: string;
  state: string;
  zipCode: string;
  lat?: number;
  lng?: number;
};

interface Job {
  _id?: string;
  clientId: string;
  jobNumber: string;
  jobName: string;
  additionalInfo?: string;
  locations?: LocationData[];
  createdAt?: string;
}

const emptyJob: Job = {
  clientId: '',
  jobNumber: '',
  jobName: '',
  additionalInfo: '',
  locations: [],
};

// Helper functions to convert between frontend and backend location formats
const mapLocationFromBackend = (backendLoc: BackendLocationData): LocationData => ({
  street: backendLoc.address1 || '',
  city: backendLoc.city || '',
  state: backendLoc.state,
  postalCode: backendLoc.zipCode || '',
  latitude: backendLoc.lat || 0,
  longitude: backendLoc.lng || 0,
  address1: backendLoc.address1,
  zipCode: backendLoc.zipCode,
});

const mapLocation = (frontendLoc: LocationData): BackendLocationData => ({
  address1: frontendLoc.address1 || frontendLoc.street || '',
  city: frontendLoc.city,
  state: frontendLoc.state || '',
  zipCode: frontendLoc.zipCode || frontendLoc.postalCode || '',
  lat: frontendLoc.latitude,
  lng: frontendLoc.longitude,
});

// Helper to map frontend Location to backend Location schema
function mapLocation(loc: any) {
  return {
    address1: loc.street,
    city: loc.city,
    state: loc.state || '', // fallback if not present
    zipCode: loc.postalCode,
    lat: loc.latitude,
    lng: loc.longitude,
  };
}

// Helper to map backend Location to frontend Location schema
function mapLocationFromBackend(loc: any) {
  return {
    street: loc.address1,
    city: loc.city,
    state: loc.state,
    postalCode: loc.zipCode,
    latitude: loc.lat,
    longitude: loc.lng,
  };
}

const JobManagement: React.FC = () => {
  const [clients, setClients] = useState<Client[]>([]);
  const [jobs, setJobs] = useState<Job[]>([]);
  const [jobSearch, setJobSearch] = useState('');
  const [jobModalOpen, setJobModalOpen] = useState(false);
  const [editingJob, setEditingJob] = useState<Job | null>(null);
  const [jobForm, setJobForm] = useState<Job>({...emptyJob});
  const [jobLoading, setJobLoading] = useState(false);
  const [jobImporting, setJobImporting] = useState(false);
  const [jobFormErrors, setJobFormErrors] = useState<any>({});
  const [showAll, setShowAll] = useState(false);

  useEffect(() => {
    fetchClients();
    fetchJobs();
  }, []);

  const fetchClients = async () => {
    try {
      const res = await fetch('/api/clients');
      const data = await res.json();
      setClients(data);
    } catch (error) {
      console.error('Failed to fetch clients:', error);
    }
  };

  const fetchJobs = async () => {
    setJobLoading(true);
    try {
      const res = await fetch('/api/jobs');
      const data = await res.json();
      // Map backend location data to frontend format
      const mappedData = data.map((job: any) => ({
        ...job,
        locations: (job.locations || []).map(mapLocationFromBackend)
      }));
      // Sort by creation date (newest first) or by name if no creation date
      const sortedData = mappedData.sort((a: Job, b: Job) => {
        if (a.createdAt && b.createdAt) {
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        }
        return a.jobName.localeCompare(b.jobName);
      });
      setJobs(sortedData);
    } catch (error) {
      console.error('Failed to fetch jobs:', error);
    }
    setJobLoading(false);
  };

  const openAddJobModal = () => {
    setEditingJob(null);
    setJobForm(emptyJob);
    setJobFormErrors({});
    setJobModalOpen(true);
  };

  const openEditJobModal = (job: Job) => {
    setEditingJob(job);
    setJobForm({
      ...job,
      locations: (job.locations || []).map(mapLocationFromBackend),
    });
    setJobFormErrors({});
    setJobModalOpen(true);
  };

  const closeJobModal = () => {
    setJobModalOpen(false);
    setEditingJob(null);
    setJobForm(emptyJob);
    setJobFormErrors({});
  };

  const handleJobFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setJobForm({ ...jobForm, [name]: value });
    setJobFormErrors({ ...jobFormErrors, [name]: undefined });
  };

  const validateJobForm = () => {
    const errors: any = {};
    if (!jobForm.clientId) errors.clientId = 'Client is required';
    if (!jobForm.jobNumber) errors.jobNumber = 'Job # is required';
    if (!jobForm.jobName) errors.jobName = 'Job Name is required';
    if (!jobForm.locations || jobForm.locations.length === 0) errors.locations = 'At least one location is required';
    if (jobForm.locations) {
      jobForm.locations.forEach((loc, idx) => {
        if (!loc.street || !loc.city || !loc.state || !loc.postalCode) {
          errors[`location_${idx}`] = 'All address fields are required';
        }
      });
    }
    setJobFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleJobSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateJobForm()) return;
    setJobLoading(true);
    try {
      const jobToSend = {
        ...jobForm,
        locations: (jobForm.locations || []).map(mapLocation),
      };
      if (editingJob && editingJob._id) {
        await fetch(`/api/jobs/${editingJob._id}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(jobToSend),
        });
      } else {
        await fetch('/api/jobs', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(jobToSend),
        });
      }
      closeJobModal();
      fetchJobs();
    } catch (error) {
      console.error('Failed to save job:', error);
    }
    setJobLoading(false);
  };

  const handleJobDelete = async (id: string) => {
    if (!window.confirm('Delete this job?')) return;
    setJobLoading(true);
    try {
      await fetch(`/api/jobs/${id}`, { method: 'DELETE' });
      fetchJobs();
    } catch (error) {
      console.error('Failed to delete job:', error);
    }
    setJobLoading(false);
  };

  // Filtered jobs
  const filteredJobs = jobs.filter(j =>
    j.jobName.toLowerCase().includes(jobSearch.toLowerCase()) ||
    j.jobNumber.toLowerCase().includes(jobSearch.toLowerCase())
  );

  // Display jobs (show top 5 or all based on showAll state, or filtered results)
  const displayJobs = jobSearch ? filteredJobs : (showAll ? jobs : jobs.slice(0, 5));

  const addJobLocation = () => setJobForm(jf => ({ ...jf, locations: [...(jf.locations || []), { street: '', city: '', state: '', postalCode: '', latitude: 0, longitude: 0, address1: '', zipCode: '' }] }));
  const updateJobLocation = (idx: number, loc: LocationData) => setJobForm(jf => ({ ...jf, locations: (jf.locations || []).map((l, i) => i === idx ? loc : l) }));
  const removeJobLocation = (idx: number) => setJobForm(jf => ({ ...jf, locations: (jf.locations || []).filter((_, i) => i !== idx) }));

  return (
    <div className="w-full space-y-6">
      <div className="bg-white rounded-2xl shadow-lg p-6 border border-gray-100">
        <h2 className="text-2xl font-bold mb-6 text-gray-800 flex items-center gap-2">
          💼 Job Management
        </h2>
        
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
          <div className="flex gap-3">
            <div className="relative">
              <input
                type="text"
                placeholder="🔍 Search by job name or #..."
                className="border border-gray-300 rounded-xl px-4 py-3 pl-10 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 shadow-sm"
                value={jobSearch}
                onChange={e => setJobSearch(e.target.value)}
              />
              <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                🔍
              </div>
            </div>
            <button
              className="bg-gradient-to-r from-blue-500 to-blue-600 text-white px-6 py-3 rounded-xl font-semibold hover:from-blue-600 hover:to-blue-700 shadow-md transition-all duration-200 transform hover:scale-105"
              onClick={openAddJobModal}
            >
              ➕ Add Job
            </button>
          </div>
          <button
            className="bg-gradient-to-r from-green-500 to-green-600 text-white px-6 py-3 rounded-xl font-semibold hover:from-green-600 hover:to-green-700 shadow-md transition-all duration-200 transform hover:scale-105"
            onClick={() => setJobImporting(true)}
          >
            📥 Import CSV/XLS
          </button>
        </div>

        {/* Show/Hide toggle for non-filtered results */}
        {!jobSearch && jobs.length > 5 && (
          <div className="mb-4">
            <button
              className="text-blue-600 hover:text-blue-800 text-sm font-semibold"
              onClick={() => setShowAll(!showAll)}
            >
              {showAll ? `Hide (showing all ${jobs.length} jobs)` : `Show all ${jobs.length} jobs (currently showing top 5)`}
            </button>
          </div>
        )}

        <div className="w-full overflow-x-auto rounded shadow bg-white border max-h-[60vh] overflow-y-auto">
          <table className="w-full min-w-full text-sm">
            <thead className="bg-gray-50 sticky top-0">
              <tr>
                <th className="p-3 text-left font-semibold text-gray-700">System ID</th>
                <th className="p-3 text-left font-semibold text-gray-700">Client</th>
                <th className="p-3 text-left font-semibold text-gray-700">Job #</th>
                <th className="p-3 text-left font-semibold text-gray-700">Job Name</th>
                <th className="p-3 text-left font-semibold text-gray-700">Job Locations</th>
                <th className="p-3 text-left font-semibold text-gray-700">Actions</th>
              </tr>
            </thead>
            <tbody>
              {jobLoading ? (
                <tr><td colSpan={6} className="p-4 text-center">Loading...</td></tr>
              ) : displayJobs.length === 0 ? (
                <tr><td colSpan={6} className="p-4 text-center">No jobs found.</td></tr>
              ) : displayJobs.map(job => (
                <tr key={job._id} className="border-b hover:bg-gray-50">
                  <td className="p-2 font-mono text-xs">{job._id}</td>
                  <td className="p-2">{clients.find(c => c._id === job.clientId)?.name || '—'}</td>
                  <td className="p-2">{job.jobNumber}</td>
                  <td className="p-2 font-semibold">{job.jobName}</td>
                  <td className="p-2">
                    {job.locations && job.locations.length > 0 ? (
                      <ul className="space-y-1">
                        {job.locations.map((loc, idx) => {
                          const fullAddress = [loc.address1 || loc.street, loc.city, loc.state, loc.zipCode || loc.postalCode].filter(Boolean).join(', ');
                          return (
                            <li key={idx} className="text-sm">
                              <div className="font-medium">{fullAddress}</div>
                            </li>
                          );
                        })}
                      </ul>
                    ) : (
                      <span className="text-gray-400">—</span>
                    )}
                  </td>
                  <td className="p-2 flex gap-2">
                    <button className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs" onClick={() => openEditJobModal(job)}>Edit</button>
                    <button className="px-2 py-1 bg-red-100 text-red-800 rounded text-xs" onClick={() => handleJobDelete(job._id!)}>Delete</button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Add/Edit Job Modal */}
      {jobModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-2xl relative max-h-[95vh] overflow-hidden flex flex-col">
            <div className="flex-shrink-0 p-6 border-b">
              <button className="absolute top-4 right-4 text-gray-500 hover:text-gray-800 text-2xl bg-white rounded-full shadow-sm p-2 focus:outline-none focus:ring-2 focus:ring-blue-400" onClick={closeJobModal}>&times;</button>
              <h3 className="text-xl font-bold pr-8">{editingJob ? 'Edit Job' : 'Add Job'}</h3>
            </div>
            <div className="flex-1 overflow-y-auto p-6">
              <form className="grid grid-cols-1 md:grid-cols-2 gap-4" onSubmit={handleJobSubmit}>
                <div>
                  <label className="block font-semibold mb-1">Client</label>
                  <select name="clientId" value={jobForm.clientId} onChange={handleJobFormChange} className="w-full border rounded px-2 py-1" required>
                    <option value="">Select client...</option>
                    {clients.map(c => (
                      <option key={c._id} value={c._id}>{c.name}</option>
                    ))}
                  </select>
                  {jobFormErrors.clientId && <div className="text-xs text-red-600 mt-1">{jobFormErrors.clientId}</div>}
                </div>
                <div>
                  <label className="block font-semibold mb-1">Job #</label>
                  <input name="jobNumber" value={jobForm.jobNumber} onChange={handleJobFormChange} className="w-full border rounded px-2 py-1" required />
                  {jobFormErrors.jobNumber && <div className="text-xs text-red-600 mt-1">{jobFormErrors.jobNumber}</div>}
                </div>
                <div>
                  <label className="block font-semibold mb-1">Job Name</label>
                  <input name="jobName" value={jobForm.jobName} onChange={handleJobFormChange} className="w-full border rounded px-2 py-1" required />
                  {jobFormErrors.jobName && <div className="text-xs text-red-600 mt-1">{jobFormErrors.jobName}</div>}
                </div>
                <div className="md:col-span-2">
                  <label className="block font-semibold mb-1">Job Locations</label>
                  {jobFormErrors.locations && <div className="text-xs text-red-600 mb-1">{jobFormErrors.locations}</div>}
                  {(jobForm.locations || []).map((loc, idx) => (
                    <div key={idx} className="mb-2 p-2 border rounded bg-gray-50 flex items-center gap-2 relative z-10">
                      <LocationInput
                        location={loc}
                        onChange={l => updateJobLocation(idx, l)}
                        label={`Location ${idx + 1}`}
                        placeholder="Search for job location..."
                      />
                      <button type="button" className="ml-2 px-2 py-1 bg-red-100 text-red-800 rounded text-xs" onClick={() => removeJobLocation(idx)}>Remove</button>
                      {jobFormErrors[`location_${idx}`] && <div className="text-xs text-red-600 absolute left-0 bottom-[-18px]">{jobFormErrors[`location_${idx}`]}</div>}
                    </div>
                  ))}
                  <button type="button" className="mt-2 px-3 py-1 bg-blue-600 text-white rounded text-sm font-semibold hover:bg-blue-700" onClick={addJobLocation}>+ Add Location</button>
                </div>
                <div className="md:col-span-2">
                  <label className="block font-semibold mb-1">Additional Info</label>
                  <textarea name="additionalInfo" value={jobForm.additionalInfo} onChange={handleJobFormChange} className="w-full border rounded px-2 py-1" rows={2} />
                </div>
              </form>
            </div>
            <div className="flex-shrink-0 p-6 border-t bg-gray-50">
              <div className="flex justify-end gap-2">
                <button type="button" className="px-4 py-2 bg-gray-200 rounded" onClick={closeJobModal}>Cancel</button>
                <button type="submit" className="px-4 py-2 bg-blue-600 text-white rounded font-semibold hover:bg-blue-700" onClick={handleJobSubmit}>{editingJob ? 'Update' : 'Add'} Job</button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Import Modal (UI only for now) */}
      {jobImporting && (
        <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-lg relative">
            <button className="absolute top-4 right-4 text-gray-500 hover:text-gray-800 text-2xl" onClick={() => setJobImporting(false)}>&times;</button>
            <div className="p-6">
              <h3 className="text-xl font-bold mb-4 pr-8">Import Jobs from CSV/XLS</h3>
              <p className="mb-4 text-gray-600">Upload a CSV or Excel file with columns matching the job fields. (Import logic coming soon.)</p>
              <input type="file" accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel" className="mb-4 w-full" />
              <div className="flex justify-end gap-2">
                <button className="px-4 py-2 bg-gray-200 rounded" onClick={() => setJobImporting(false)}>Cancel</button>
                <button className="px-4 py-2 bg-green-600 text-white rounded font-semibold hover:bg-green-700" disabled>Import</button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default JobManagement;
