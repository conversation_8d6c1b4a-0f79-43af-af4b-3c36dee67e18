"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const PurchaseOrder_1 = require("../models/PurchaseOrder");
const authMiddleware_1 = require("../middleware/authMiddleware");
const mongoose_1 = __importDefault(require("mongoose"));
const uploadMiddleware_1 = __importDefault(require("../middleware/uploadMiddleware"));
const sharp_1 = __importDefault(require("sharp"));
const email_1 = require("../utils/email");
const User_1 = __importDefault(require("../models/User"));
// Simple helper to convert <PERSON>uffer to base64 string
function bufferToBase64(buffer) {
    return buffer.toString('base64');
}
// Helper function to get delivery location address string
const getDeliveryLocationAddress = (deliveryLocation) => {
    if (typeof deliveryLocation === 'string') {
        return deliveryLocation;
    }
    if (deliveryLocation && typeof deliveryLocation === 'object') {
        const parts = [
            deliveryLocation.street,
            deliveryLocation.city,
            deliveryLocation.state,
            deliveryLocation.postalCode
        ].filter(Boolean);
        return parts.length > 0 ? parts.join(', ') : 'No address specified';
    }
    return 'No address specified';
};
const router = express_1.default.Router();
// POST /api/purchase-orders → Create PO
router.post('/', async (req, res) => {
    try {
        const { clientName, pickupLocation, loads, materials } = req.body;
        // Debug logging to see what data is being received
        console.log('Creating PO with data:');
        console.log('clientName:', clientName);
        console.log('pickupLocation:', JSON.stringify(pickupLocation, null, 2));
        console.log('loads:', JSON.stringify(loads, null, 2));
        console.log('materials:', JSON.stringify(materials, null, 2));
        if (!clientName || !pickupLocation || !Array.isArray(loads)) {
            return res.status(400).json({ error: 'Missing or invalid required fields' });
        }
        // Ensure all loads have deliveryLocation objects (even if empty)
        const processedLoads = loads.map(load => ({
            ...load,
            deliveryLocation: load.deliveryLocation || {
                street: '',
                city: '',
                state: '',
                postalCode: '',
                latitude: 0,
                longitude: 0
            }
        }));
        // Find the current max orderNumber
        const lastPO = await PurchaseOrder_1.PurchaseOrder.findOne().sort({ orderNumber: -1 });
        const lastOrderNumber = lastPO && typeof lastPO.orderNumber === 'number' && !isNaN(lastPO.orderNumber) ? lastPO.orderNumber : 0;
        const nextOrderNumber = lastOrderNumber + 1;
        const po = new PurchaseOrder_1.PurchaseOrder({
            clientName,
            orderNumber: nextOrderNumber,
            pickupLocation,
            materials: Array.isArray(materials) ? materials : [],
            loads: processedLoads,
        });
        await po.save();
        res.status(201).json(po);
    }
    catch (err) {
        console.error(err);
        res.status(500).json({ error: 'Failed to create PO' });
    }
});
// ✅ GET /api/purchase-orders → Fetch all POs
router.get('/', authMiddleware_1.verifyToken, async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 20;
        const skip = (page - 1) * limit;
        const usePagination = req.query.page !== undefined || req.query.limit !== undefined;
        // Restrict truckers to only see assigned POs
        if (req.user && req.user.role === 'trucker') {
            // Find POs where any transaction.truckerId matches req.user.id
            const orders = await PurchaseOrder_1.PurchaseOrder.find({
                'transactions.truckerId': req.user.id
            }).populate('transactions.truckerId', 'username email').sort({ createdAt: -1 });
            // Calculate remaining quantities for trucker orders
            const ordersWithRemainingQuantities = orders.map(po => {
                const poObj = po.toObject();
                if (poObj.loads) {
                    poObj.loads = poObj.loads.map(load => {
                        if (load.materials) {
                            load.materials = load.materials.map(material => {
                                const loadLocationNormalized = getDeliveryLocationAddress(load.deliveryLocation);
                                let deliveredQty = (poObj.totalDeliveredQuantities || [])
                                    .filter(td => td.materialId.toString() === material.materialId.toString() &&
                                    getDeliveryLocationAddress(td.deliveryLocation) === loadLocationNormalized)
                                    .reduce((sum, td) => sum + td.quantity, 0);
                                // Fallback: Calculate from transactions if totalDeliveredQuantities is empty
                                if (deliveredQty === 0 && poObj.transactions) {
                                    deliveredQty = poObj.transactions
                                        .filter(tx => tx.materialsDelivered && tx.materialsDelivered.length > 0)
                                        .flatMap(tx => tx.materialsDelivered || [])
                                        .filter(md => md && md.materialId && md.deliveryLocationName &&
                                        md.materialId.toString() === material.materialId.toString() &&
                                        getDeliveryLocationAddress(md.deliveryLocationName) === loadLocationNormalized)
                                        .reduce((sum, md) => sum + (md?.quantity || 0), 0);
                                }
                                return {
                                    ...material,
                                    remainingQuantity: Math.max(0, material.quantity - deliveredQty),
                                    deliveredQuantity: deliveredQty
                                };
                            });
                        }
                        return load;
                    });
                }
                return poObj;
            });
            return res.json(ordersWithRemainingQuantities);
        }
        if (usePagination) {
            const [orders, total] = await Promise.all([
                PurchaseOrder_1.PurchaseOrder.find().populate('transactions.truckerId', 'username email').sort({ createdAt: -1 }).skip(skip).limit(limit),
                PurchaseOrder_1.PurchaseOrder.countDocuments()
            ]);
            // Calculate remaining quantities for paginated orders
            const ordersWithRemainingQuantities = orders.map(po => {
                const poObj = po.toObject();
                if (poObj.loads) {
                    poObj.loads = poObj.loads.map(load => {
                        if (load.materials) {
                            load.materials = load.materials.map(material => {
                                const loadLocationNormalized = getDeliveryLocationAddress(load.deliveryLocation);
                                let deliveredQty = (poObj.totalDeliveredQuantities || [])
                                    .filter(td => td.materialId.toString() === material.materialId.toString() &&
                                    getDeliveryLocationAddress(td.deliveryLocation) === loadLocationNormalized)
                                    .reduce((sum, td) => sum + td.quantity, 0);
                                // Fallback: Calculate from transactions if totalDeliveredQuantities is empty
                                if (deliveredQty === 0 && poObj.transactions) {
                                    deliveredQty = poObj.transactions
                                        .filter(tx => tx.materialsDelivered && tx.materialsDelivered.length > 0)
                                        .flatMap(tx => tx.materialsDelivered || [])
                                        .filter(md => md && md.materialId && md.deliveryLocationName &&
                                        md.materialId.toString() === material.materialId.toString() &&
                                        getDeliveryLocationAddress(md.deliveryLocationName) === loadLocationNormalized)
                                        .reduce((sum, md) => sum + (md?.quantity || 0), 0);
                                }
                                return {
                                    ...material,
                                    remainingQuantity: Math.max(0, material.quantity - deliveredQty),
                                    deliveredQuantity: deliveredQty
                                };
                            });
                        }
                        return load;
                    });
                }
                return poObj;
            });
            return res.json({ orders: ordersWithRemainingQuantities, total, page, limit });
        }
        else {
            // Default: return all orders as before
            const orders = await PurchaseOrder_1.PurchaseOrder.find().populate('transactions.truckerId', 'username email').sort({ createdAt: -1 });
            // Calculate remaining quantities for each material in each load
            const ordersWithRemainingQuantities = orders.map(po => {
                const poObj = po.toObject();
                // Debug: Show totalDeliveredQuantities for this PO
                if (poObj.totalDeliveredQuantities && poObj.totalDeliveredQuantities.length > 0) {
                    console.log(`🚚 PO ${poObj.orderNumber} - totalDeliveredQuantities:`, JSON.stringify(poObj.totalDeliveredQuantities, null, 2));
                }
                if (poObj.loads) {
                    poObj.loads = poObj.loads.map(load => {
                        if (load.materials) {
                            load.materials = load.materials.map(material => {
                                // Calculate delivered quantity for this material at this location
                                // Debug: Show all delivery data for this material
                                const allDeliveries = (poObj.totalDeliveredQuantities || [])
                                    .filter(td => td.materialId.toString() === material.materialId.toString());
                                const loadLocationNormalized = getDeliveryLocationAddress(load.deliveryLocation);
                                console.log(`🔍 DELIVERY MATCHING DEBUG for ${material.materialName}:`);
                                console.log(`  Load location (normalized): "${loadLocationNormalized}"`);
                                console.log(`  All deliveries for this material:`, allDeliveries.map(td => ({
                                    deliveryLocation: td.deliveryLocation,
                                    normalized: getDeliveryLocationAddress(td.deliveryLocation),
                                    quantity: td.quantity,
                                    matches: getDeliveryLocationAddress(td.deliveryLocation) === loadLocationNormalized
                                })));
                                let deliveredQty = allDeliveries
                                    .filter(td => getDeliveryLocationAddress(td.deliveryLocation) === loadLocationNormalized)
                                    .reduce((sum, td) => sum + td.quantity, 0);
                                // Fallback: If totalDeliveredQuantities is empty or not matching,
                                // calculate from transactions.materialsDelivered
                                if (deliveredQty === 0 && poObj.transactions) {
                                    console.log(`⚠️ Fallback calculation for ${material.materialName}`);
                                    deliveredQty = poObj.transactions
                                        .filter(tx => tx.materialsDelivered && tx.materialsDelivered.length > 0)
                                        .flatMap(tx => tx.materialsDelivered || [])
                                        .filter(md => md && md.materialId && md.deliveryLocationName &&
                                        md.materialId.toString() === material.materialId.toString() &&
                                        getDeliveryLocationAddress(md.deliveryLocationName) === loadLocationNormalized)
                                        .reduce((sum, md) => sum + (md?.quantity || 0), 0);
                                    console.log(`📊 FALLBACK CALC - Found ${deliveredQty} delivered from transactions`);
                                }
                                const remainingQty = Math.max(0, material.quantity - deliveredQty);
                                console.log(`📊 FINAL CALC - Material: ${material.materialName}, Original: ${material.quantity}, Delivered: ${deliveredQty}, Remaining: ${remainingQty}`);
                                return {
                                    ...material,
                                    remainingQuantity: remainingQty,
                                    deliveredQuantity: deliveredQty
                                };
                            });
                        }
                        return load;
                    });
                }
                return poObj;
            });
            return res.json(ordersWithRemainingQuantities);
        }
    }
    catch (err) {
        console.error(err);
        res.status(500).json({ error: 'Failed to fetch purchase orders' });
    }
});
router.post('/:poId/complete/:transactionId', authMiddleware_1.verifyToken, uploadMiddleware_1.default.single('ticket'), async (req, res) => {
    try {
        const { poId, transactionId } = req.params;
        console.log('➡️ POST /:poId/complete/:transactionId');
        console.log('poId:', poId);
        console.log('transactionId:', transactionId);
        const user = req.user;
        if (!user)
            return res.status(401).json({ message: 'Unauthorized' });
        const { materialsData, signature, action } = req.body; // action can be 'pickup' or 'delivery'
        let parsedMaterials;
        try {
            parsedMaterials = typeof materialsData === 'string' ? JSON.parse(materialsData) : materialsData;
        }
        catch {
            return res.status(400).json({ message: 'Invalid materialsData format' });
        }
        if (!parsedMaterials || !Array.isArray(parsedMaterials) || parsedMaterials.length === 0) {
            return res.status(400).json({ message: 'materialsData is required and must be an array' });
        }
        if (!signature) {
            return res.status(400).json({ message: 'Signature is required' });
        }
        if (!action || !['pickup', 'delivery'].includes(action)) {
            return res.status(400).json({ message: 'Action must be either "pickup" or "delivery"' });
        }
        const po = await PurchaseOrder_1.PurchaseOrder.findById(poId);
        if (!po) {
            console.log('❌ PO not found for ID:', poId);
            return res.status(404).json({ message: 'Purchase order not found' });
        }
        const transaction = po.transactions.find((t) => t._id && t._id.toString() === transactionId);
        if (!transaction) {
            console.log('❌ Transaction not found. Searched for ID:', transactionId);
            return res.status(404).json({ message: 'Transaction not found' });
        }
        if (transaction.truckerId.toString() !== user.id) {
            return res.status(403).json({ message: 'You are not authorized to complete this transaction' });
        }
        if (transaction.status === 'Completed') {
            return res.status(400).json({ message: 'Transaction already completed' });
        }
        // Handle pickup action
        if (action === 'pickup') {
            if (transaction.status !== 'Reserved') {
                return res.status(400).json({ message: 'Transaction is not in reserved state' });
            }
            transaction.signature = signature;
            transaction.materialsPickedUp = parsedMaterials;
            transaction.pickedUpAt = new Date();
            transaction.status = 'PickedUp';
            if (req.file) {
                try {
                    const resizedBuffer = await (0, sharp_1.default)(req.file.buffer)
                        .resize({ width: 1024, withoutEnlargement: true })
                        .jpeg({ quality: 80 })
                        .toBuffer();
                    transaction.pickupTicketImage = resizedBuffer.toString('base64');
                }
                catch (err) {
                    console.error('Failed to process image with sharp:', err);
                    return res.status(500).json({ error: 'Failed to process pickup ticket image' });
                }
            }
            await po.save();
            res.status(200).json({ message: 'Pickup completed successfully' });
        }
        // Handle delivery action
        else if (action === 'delivery') {
            if (transaction.status !== 'PickedUp') {
                return res.status(400).json({ message: 'Transaction is not in picked up state' });
            }
            transaction.deliverySignature = signature;
            transaction.materialsDelivered = parsedMaterials;
            transaction.completedAt = new Date();
            transaction.status = 'Completed';
            if (req.file) {
                try {
                    const resizedBuffer = await (0, sharp_1.default)(req.file.buffer)
                        .resize({ width: 1024, withoutEnlargement: true })
                        .jpeg({ quality: 80 })
                        .toBuffer();
                    transaction.deliveryTicketImage = resizedBuffer.toString('base64');
                }
                catch (err) {
                    console.error('Failed to process image with sharp:', err);
                    return res.status(500).json({ error: 'Failed to process delivery ticket image' });
                }
            }
            // Update total delivered quantities and check for completion
            let overDeliveryWarnings = [];
            // Initialize totalDeliveredQuantities if it doesn't exist
            if (!po.totalDeliveredQuantities) {
                po.totalDeliveredQuantities = [];
            }
            console.log('🔍 DELIVERY PROCESSING DEBUG:');
            console.log('PO loads:', JSON.stringify(po.loads, null, 2));
            console.log('Parsed materials being delivered:', JSON.stringify(parsedMaterials, null, 2));
            for (const deliveredMaterial of parsedMaterials) {
                // Find the original material to get its total quantity from delivery locations
                let originalMaterial = null;
                let originalLoad = null;
                for (const load of po.loads) {
                    originalMaterial = load.materials.find(m => m.materialId.toString() === deliveredMaterial.materialId);
                    if (originalMaterial) {
                        originalLoad = load;
                        break;
                    }
                }
                if (!originalMaterial || !originalLoad)
                    continue;
                const originalQuantity = originalMaterial.quantity;
                // Find existing delivered quantity for this material at this location
                // Use normalized location for consistent matching
                const normalizedDeliveryLocation = getDeliveryLocationAddress(originalLoad?.deliveryLocation) || deliveredMaterial.deliveryLocationName;
                let existingDelivered = po.totalDeliveredQuantities.find(td => td.materialId.toString() === deliveredMaterial.materialId.toString() &&
                    (td.deliveryLocation === normalizedDeliveryLocation || td.deliveryLocation === deliveredMaterial.deliveryLocationName));
                console.log(`📦 Processing delivery for material ${deliveredMaterial.materialId}:`);
                console.log(`  Delivery location name: "${deliveredMaterial.deliveryLocationName}"`);
                console.log(`  Load location (original):`, originalLoad?.deliveryLocation);
                console.log(`  Load location (normalized): "${getDeliveryLocationAddress(originalLoad?.deliveryLocation)}"`);
                console.log(`  Existing delivered:`, existingDelivered);
                if (existingDelivered) {
                    existingDelivered.quantity += deliveredMaterial.quantity;
                    console.log(`  ✅ Updated existing delivery to ${existingDelivered.quantity}`);
                }
                else {
                    // IMPORTANT: Store the normalized delivery location to ensure consistent matching
                    // (normalizedDeliveryLocation already defined above)
                    po.totalDeliveredQuantities.push({
                        materialId: deliveredMaterial.materialId,
                        deliveryLocation: normalizedDeliveryLocation,
                        quantity: deliveredMaterial.quantity
                    });
                    existingDelivered = po.totalDeliveredQuantities[po.totalDeliveredQuantities.length - 1];
                    console.log(`  ✅ Added new delivery record with normalized location:`, existingDelivered);
                    console.log(`  📍 Original delivery location: "${deliveredMaterial.deliveryLocationName}"`);
                    console.log(`  📍 Normalized delivery location: "${normalizedDeliveryLocation}"`);
                }
                // Check for over-delivery at this location
                if (existingDelivered.quantity > originalQuantity) {
                    overDeliveryWarnings.push({
                        materialName: originalMaterial.materialName,
                        location: deliveredMaterial.deliveryLocationName,
                        requested: originalQuantity,
                        delivered: existingDelivered.quantity,
                        overage: existingDelivered.quantity - originalQuantity
                    });
                }
            }
            // Check completion status for each delivery location
            let allLocationsCompleted = true;
            let currentLocationCompleted = true;
            const currentDeliveryLocation = parsedMaterials[0]?.deliveryLocationName;
            console.log('🔍 COMPLETION DEBUG:');
            console.log('Current delivery location:', currentDeliveryLocation);
            console.log('PO loads:', JSON.stringify(po.loads, null, 2));
            console.log('Total delivered quantities:', JSON.stringify(po.totalDeliveredQuantities, null, 2));
            // Debug: Show normalized delivery locations
            if (po.loads) {
                console.log('Normalized delivery locations:');
                po.loads.forEach((load, idx) => {
                    console.log(`  Load ${idx}: "${getDeliveryLocationAddress(load.deliveryLocation)}"`);
                });
            }
            // Filter out loads that don't have materials (empty loads)
            const validLoads = po.loads.filter(load => load.materials && load.materials.length > 0);
            console.log('Valid loads count:', validLoads.length);
            // Get unique delivery locations from totalDeliveredQuantities to handle legacy POs
            const deliveredLocations = [...new Set(po.totalDeliveredQuantities.map(td => td.deliveryLocation))];
            console.log('Delivered locations:', deliveredLocations);
            // If we have delivered quantities but no valid loads with delivery locations,
            // this is likely a legacy PO - check completion based on delivered quantities only
            const hasValidDeliveryLocations = validLoads.some(load => load.deliveryLocation && getDeliveryLocationAddress(load.deliveryLocation) !== 'No address specified');
            console.log('Has valid delivery locations:', hasValidDeliveryLocations);
            if (!hasValidDeliveryLocations && deliveredLocations.length > 0) {
                // Legacy PO completion check: compare total materials vs delivered quantities
                console.log('🔄 Using legacy completion check for PO without proper delivery locations');
                // Get all materials from all loads
                const allMaterials = validLoads.flatMap(load => load.materials);
                console.log('All materials to check:', allMaterials);
                // Check if all materials have been delivered in sufficient quantities
                for (const material of allMaterials) {
                    const totalDelivered = po.totalDeliveredQuantities
                        .filter(td => td.materialId.toString() === material.materialId.toString())
                        .reduce((sum, td) => sum + td.quantity, 0);
                    console.log(`Material ${material.materialId}: required=${material.quantity}, delivered=${totalDelivered}`);
                    if (totalDelivered < material.quantity) {
                        console.log(`❌ Material ${material.materialId} not fully delivered`);
                        allLocationsCompleted = false;
                        currentLocationCompleted = false;
                        break;
                    }
                    else {
                        console.log(`✅ Material ${material.materialId} fully delivered`);
                    }
                }
            }
            else {
                // Normal completion check for POs with proper delivery locations
                console.log('🔄 Using normal completion check for PO with proper delivery locations');
                for (const load of validLoads) {
                    let locationCompleted = true;
                    const loadLocationAddress = getDeliveryLocationAddress(load.deliveryLocation);
                    console.log(`Checking load with location: "${loadLocationAddress}"`);
                    for (const material of load.materials) {
                        const delivered = po.totalDeliveredQuantities.find(td => td.materialId.toString() === material.materialId.toString() &&
                            getDeliveryLocationAddress(td.deliveryLocation) === loadLocationAddress);
                        console.log(`Material ${material.materialId}: required=${material.quantity}, delivered=${delivered?.quantity || 0}, location match: "${loadLocationAddress}" === "${delivered?.deliveryLocation}"`);
                        if (!delivered || delivered.quantity < material.quantity) {
                            console.log(`❌ Material ${material.materialId} not fully delivered at location "${loadLocationAddress}"`);
                            locationCompleted = false;
                            if (loadLocationAddress === currentDeliveryLocation) {
                                currentLocationCompleted = false;
                            }
                            // Don't break here - continue checking all materials for this location
                        }
                        else {
                            console.log(`✅ Material ${material.materialId} fully delivered at location "${loadLocationAddress}"`);
                        }
                    }
                    // If this location is not completed, mark overall completion as false
                    if (!locationCompleted) {
                        allLocationsCompleted = false;
                        console.log(`❌ Location "${loadLocationAddress}" not completed`);
                    }
                    else {
                        console.log(`✅ Location "${loadLocationAddress}" completed`);
                    }
                }
            }
            console.log('🏁 FINAL RESULTS:');
            console.log('All locations completed:', allLocationsCompleted);
            console.log('Current location completed:', currentLocationCompleted);
            // Only update isCompleted if ALL locations are completed
            if (allLocationsCompleted) {
                po.isCompleted = true;
                console.log('✅ PO marked as completed');
            }
            else {
                console.log('❌ PO not completed - some locations/materials still pending');
            }
            await po.save();
            console.log('💾 FINAL PO STATE AFTER SAVE:');
            console.log('totalDeliveredQuantities:', JSON.stringify(po.totalDeliveredQuantities, null, 2));
            console.log('PO loads with normalized addresses:');
            po.loads?.forEach((load, idx) => {
                console.log(`  Load ${idx}: "${getDeliveryLocationAddress(load.deliveryLocation)}"`);
            });
            const response = { message: 'Delivery completed successfully' };
            if (overDeliveryWarnings.length > 0) {
                response.warnings = overDeliveryWarnings;
            }
            if (currentLocationCompleted) {
                response.completionMessage = `Delivery location "${currentDeliveryLocation}" has been completed`;
                // Get all unique delivery locations that should exist based on loads
                const expectedLocations = new Set();
                for (const load of validLoads) {
                    const loadLocationAddress = getDeliveryLocationAddress(load.deliveryLocation);
                    if (loadLocationAddress !== 'No address specified') {
                        expectedLocations.add(loadLocationAddress);
                    }
                }
                // For legacy POs without proper delivery locations, check if we have delivered to all materials
                if (expectedLocations.size === 0) {
                    // Legacy PO: check if all materials have been delivered
                    const allMaterials = validLoads.flatMap(load => load.materials);
                    let allMaterialsDelivered = true;
                    for (const material of allMaterials) {
                        const totalDelivered = po.totalDeliveredQuantities
                            .filter(td => td.materialId.toString() === material.materialId.toString())
                            .reduce((sum, td) => sum + td.quantity, 0);
                        if (totalDelivered < material.quantity) {
                            allMaterialsDelivered = false;
                            break;
                        }
                    }
                    if (allMaterialsDelivered) {
                        response.completionMessage += '. All materials have been delivered, and this PO is marked as complete.';
                        allLocationsCompleted = true;
                    }
                    else {
                        response.completionMessage += '. There are still materials that need to be delivered.';
                    }
                }
                else {
                    // Modern PO: use the existing logic
                    if (allLocationsCompleted) {
                        response.completionMessage += '. All locations are now completed, and this PO is marked as complete.';
                    }
                    else {
                        response.completionMessage += '. There are still other locations that need deliveries.';
                    }
                }
                console.log('Expected locations:', Array.from(expectedLocations));
                console.log('All locations completed:', allLocationsCompleted);
            }
            res.status(200).json(response);
        }
    }
    catch (err) {
        console.error(err);
        res.status(500).json({ error: 'Failed to complete transaction' });
    }
});
// GET /api/purchase-orders/:poId/transactions/:transactionId/signature/:type → Get signature (pickup or delivery)
router.get('/:poId/transactions/:transactionId/signature/:type', authMiddleware_1.verifyToken, async (req, res) => {
    try {
        const { poId, transactionId, type } = req.params;
        const user = req.user;
        if (!user)
            return res.status(401).json({ message: 'Unauthorized' });
        if (!type || !['pickup', 'delivery'].includes(type)) {
            return res.status(400).json({ message: 'Type must be either "pickup" or "delivery"' });
        }
        const po = await PurchaseOrder_1.PurchaseOrder.findById(poId);
        if (!po)
            return res.status(404).json({ message: 'Purchase order not found' });
        const transaction = po.transactions.find((t) => t._id && t._id.toString() === transactionId);
        if (!transaction)
            return res.status(404).json({ message: 'Transaction not found' });
        // Allow trucker to view their own, or admin to view any
        if (user.role !== 'admin' && transaction.truckerId.toString() !== user.id) {
            return res.status(403).json({ message: 'You are not authorized to view this transaction' });
        }
        const signature = type === 'pickup' ? transaction.signature : transaction.deliverySignature;
        if (!signature) {
            return res.status(404).json({ message: `${type} signature not found` });
        }
        res.status(200).json({ signature });
    }
    catch (err) {
        console.error(err);
        res.status(500).json({ error: 'Failed to fetch signature' });
    }
});
// GET /api/purchase-orders/:poId/transactions/:transactionId/ticket/:type → Get ticket image (pickup or delivery)
router.get('/:poId/transactions/:transactionId/ticket/:type', authMiddleware_1.verifyToken, async (req, res) => {
    try {
        const { poId, transactionId, type } = req.params;
        const user = req.user;
        if (!user)
            return res.status(401).json({ message: 'Unauthorized' });
        if (!type || !['pickup', 'delivery'].includes(type)) {
            return res.status(400).json({ message: 'Type must be either "pickup" or "delivery"' });
        }
        const po = await PurchaseOrder_1.PurchaseOrder.findById(poId);
        if (!po)
            return res.status(404).json({ message: 'Purchase order not found' });
        const transaction = po.transactions.find((t) => t._id && t._id.toString() === transactionId);
        if (!transaction)
            return res.status(404).json({ message: 'Transaction not found' });
        // Allow trucker to view their own, or admin to view any
        if (user.role !== 'admin' && transaction.truckerId.toString() !== user.id) {
            return res.status(403).json({ message: 'You are not authorized to view this transaction' });
        }
        const ticketImage = type === 'pickup' ? transaction.pickupTicketImage : transaction.deliveryTicketImage;
        if (!ticketImage) {
            return res.status(404).json({ message: `${type} ticket image not found` });
        }
        res.status(200).json({ ticketImage });
    }
    catch (err) {
        console.error(err);
        res.status(500).json({ error: 'Failed to fetch ticket image' });
    }
});
// 🔄 POST /api/purchase-orders/:poId/reserve → Reserve pickup (ADMIN ONLY)
router.post('/:poId/reserve', authMiddleware_1.verifyToken, async (req, res) => {
    try {
        const { poId } = req.params;
        const user = req.user;
        if (!user)
            return res.status(401).json({ message: 'Unauthorized' });
        // Only admins can create reservations now
        if (user.role !== 'admin') {
            return res.status(403).json({ message: 'Only admins can create reservations' });
        }
        console.log('🧾 Reservation payload:', req.body);
        const { deliveryLocationName, reservedMaterials, truckerId } = req.body;
        if (!deliveryLocationName || !Array.isArray(reservedMaterials) || !truckerId) {
            return res.status(400).json({ message: 'Missing required reservation data (deliveryLocationName, reservedMaterials, truckerId)' });
        }
        const po = await PurchaseOrder_1.PurchaseOrder.findById(poId);
        if (!po)
            return res.status(404).json({ message: 'Purchase order not found' });
        // Check if trucker already has a reservation for this PO
        const alreadyReserved = po.transactions.some(t => t.truckerId.equals(truckerId) && t.status !== 'Completed');
        if (alreadyReserved) {
            return res.status(400).json({ message: 'This trucker already has an active reservation for this PO' });
        }
        // Add the transaction
        po.transactions.push({
            truckerId: new mongoose_1.default.Types.ObjectId(truckerId),
            status: 'Reserved',
            reservedAt: new Date(),
            reservedMaterials: reservedMaterials.map(rm => ({
                materialId: rm.materialId, // Keep as string/number since these are not MongoDB ObjectIds
                materialName: rm.materialName,
                quantity: rm.quantity,
                deliveryLocationName: rm.deliveryLocationName,
            })),
        });
        await po.save();
        // ✅ Get the last inserted transaction
        const newTransaction = po.transactions[po.transactions.length - 1];
        res.status(200).json({
            message: 'Pickup assigned successfully',
            transactionId: newTransaction._id, // ✅ Send this to the frontend
        });
    }
    catch (err) {
        console.error(err);
        res.status(500).json({ error: 'Failed to assign pickup' });
    }
});
// 🆕 POST /api/purchase-orders/:poId/assign → Admin assign trucker to pickup
router.post('/:poId/assign', authMiddleware_1.verifyToken, async (req, res) => {
    const session = await mongoose_1.default.startSession();
    session.startTransaction();
    try {
        const { poId } = req.params;
        const { truckerId, reservedMaterials, deliveryLocationName } = req.body;
        const user = req.user;
        if (!user || user.role !== 'admin') {
            await session.abortTransaction();
            session.endSession();
            return res.status(403).json({ message: 'Only admins can assign truckers' });
        }
        console.log('👤 Assignment payload:', req.body);
        if (!deliveryLocationName || !Array.isArray(reservedMaterials) || !truckerId) {
            await session.abortTransaction();
            session.endSession();
            return res.status(400).json({ message: 'Missing required assignment data (deliveryLocationName, reservedMaterials, truckerId)' });
        }
        const po = await PurchaseOrder_1.PurchaseOrder.findById(poId).session(session);
        if (!po) {
            await session.abortTransaction();
            session.endSession();
            return res.status(404).json({ message: 'Purchase order not found' });
        }
        // Check if this trucker already has an active reservation in ANY PO
        const activeReservation = await PurchaseOrder_1.PurchaseOrder.findOne({
            'transactions': {
                $elemMatch: {
                    truckerId: truckerId,
                    status: { $ne: 'Completed' }
                }
            }
        }).session(session);
        if (activeReservation) {
            await session.abortTransaction();
            session.endSession();
            return res.status(400).json({ message: 'This trucker already has an active assignment for another PO' });
        }
        // Add the transaction
        po.transactions.push({
            truckerId: new mongoose_1.default.Types.ObjectId(truckerId),
            status: 'Reserved',
            reservedAt: new Date(),
            reservedMaterials: reservedMaterials.map(rm => ({
                materialId: rm.materialId,
                materialName: rm.materialName,
                quantity: rm.quantity,
                deliveryLocationName: rm.deliveryLocationName,
            })),
        });
        await po.save({ session });
        await session.commitTransaction();
        session.endSession();
        // ✅ Get the last inserted transaction
        const newTransaction = po.transactions[po.transactions.length - 1];
        // Send email to trucker (outside transaction)
        try {
            const trucker = await User_1.default.findById(truckerId);
            if (trucker && trucker.email) {
                await (0, email_1.sendEmail)(trucker.email, 'You have been assigned a new job', `You have been assigned to a new job (PO #${po.orderNumber}) for client ${po.clientName}. Please check your dashboard for details.`);
            }
        }
        catch (emailErr) {
            console.error('Failed to send assignment email to trucker:', emailErr);
        }
        res.status(200).json({
            message: 'Trucker assigned successfully',
            transactionId: newTransaction._id,
        });
    }
    catch (err) {
        await session.abortTransaction();
        session.endSession();
        console.error(err);
        res.status(500).json({ error: 'Failed to assign trucker' });
    }
});
// GET /api/purchase-orders/transactions → Get completed transactions for the logged-in trucker
router.get('/transactions', authMiddleware_1.verifyToken, async (req, res) => {
    try {
        const user = req.user;
        if (!user)
            return res.status(401).json({ message: 'Unauthorized' });
        if (user.role !== 'trucker')
            return res.status(403).json({ message: 'Forbidden' });
        // Find all POs with at least one completed transaction for this trucker
        const pos = await PurchaseOrder_1.PurchaseOrder.find({
            'transactions': {
                $elemMatch: {
                    truckerId: user.id,
                    status: 'Completed'
                }
            }
        }).populate('transactions.truckerId', 'username email');
        // Flatten the completed transactions for this trucker
        const completedTransactions = [];
        for (const po of pos) {
            for (const t of po.transactions ?? []) {
                if (t.truckerId.equals(user.id) && t.status === 'Completed') {
                    completedTransactions.push({
                        orderNumber: po.orderNumber,
                        clientName: po.clientName,
                        deliveryLocation: t.reservedMaterials?.length && t.reservedMaterials[0]?.deliveryLocationName ? t.reservedMaterials[0].deliveryLocationName : '',
                        reservedMaterials: t.reservedMaterials ?? [],
                        completedAt: t.completedAt,
                        purchaseOrderId: po._id,
                        transactionId: t._id,
                    });
                }
            }
        }
        res.status(200).json(completedTransactions);
    }
    catch (err) {
        console.error(err);
        res.status(500).json({ error: 'Failed to fetch completed transactions' });
    }
});
// GET /api/purchase-orders/active-reservations → Get active (not completed) reservations for the logged-in trucker
router.get('/active-reservations', authMiddleware_1.verifyToken, async (req, res) => {
    try {
        const user = req.user;
        if (!user)
            return res.status(401).json({ message: 'Unauthorized' });
        if (user.role !== 'trucker')
            return res.status(403).json({ message: 'Forbidden' });
        // Find all POs with at least one active (not completed) reservation for this trucker
        const pos = await PurchaseOrder_1.PurchaseOrder.find({
            'transactions': {
                $elemMatch: {
                    truckerId: user.id,
                    status: { $ne: 'Completed' }
                }
            }
        }).populate('transactions.truckerId', 'username email');
        // Flatten the active reservations for this trucker
        const activeReservations = [];
        for (const po of pos) {
            for (const t of po.transactions ?? []) {
                if (t.truckerId.equals(user.id) && t.status !== 'Completed') {
                    activeReservations.push({
                        purchaseOrderId: po._id,
                        orderNumber: po.orderNumber,
                        clientName: po.clientName,
                        deliveryLocation: t.reservedMaterials?.length && t.reservedMaterials[0]?.deliveryLocationName ? t.reservedMaterials[0].deliveryLocationName : '',
                        reservedMaterials: t.reservedMaterials ?? [],
                        reservedAt: t.reservedAt,
                        transactionId: t._id,
                        pickupCompleted: t.status === 'PickedUp',
                    });
                }
            }
        }
        res.status(200).json(activeReservations);
    }
    catch (err) {
        console.error(err);
        res.status(500).json({ error: 'Failed to fetch active reservations' });
    }
});
// DELETE /api/purchase-orders/active → Remove all active (not completed) purchase orders (admin only)
router.delete('/active', authMiddleware_1.verifyToken, async (req, res) => {
    try {
        const user = req.user;
        if (!user || user.role !== 'admin')
            return res.status(403).json({ message: 'Forbidden' });
        // Remove all POs that have at least one transaction not completed, or just remove all POs
        await PurchaseOrder_1.PurchaseOrder.deleteMany({
            $or: [
                { transactions: { $elemMatch: { status: { $ne: 'Completed' } } } },
                { transactions: { $size: 0 } }
            ]
        });
        res.status(200).json({ message: 'All active purchase orders removed' });
    }
    catch (err) {
        console.error(err);
        res.status(500).json({ error: 'Failed to remove active purchase orders' });
    }
});
// POST /api/purchase-orders/:poId/cancel/:transactionId → Cancel a reservation
router.post('/:poId/cancel/:transactionId', authMiddleware_1.verifyToken, async (req, res) => {
    try {
        const { poId, transactionId } = req.params;
        const user = req.user;
        if (!user)
            return res.status(401).json({ message: 'Unauthorized' });
        const po = await PurchaseOrder_1.PurchaseOrder.findById(poId);
        if (!po)
            return res.status(404).json({ message: 'Purchase order not found' });
        const transactionIndex = po.transactions.findIndex((t) => t._id && t._id.toString() === transactionId);
        if (transactionIndex === -1)
            return res.status(404).json({ message: 'Transaction not found' });
        const transaction = po.transactions[transactionIndex];
        if (!transaction)
            return res.status(404).json({ message: 'Transaction not found' });
        if (!transaction.truckerId.equals(user.id)) {
            return res.status(403).json({ message: 'You are not authorized to cancel this transaction' });
        }
        if (transaction.status === 'Completed') {
            return res.status(400).json({ message: 'Cannot cancel a completed transaction' });
        }
        // Remove the transaction from the array
        po.transactions.splice(transactionIndex, 1);
        await po.save();
        res.status(200).json({ message: 'Reservation cancelled successfully' });
    }
    catch (err) {
        console.error(err);
        res.status(500).json({ error: 'Failed to cancel reservation' });
    }
});
// PUT /api/purchase-orders/:poId/update-loads → Update delivery materials for a PO
router.put('/:poId/update-loads', authMiddleware_1.verifyToken, async (req, res) => {
    try {
        const { poId } = req.params;
        const user = req.user;
        if (!user || user.role !== 'admin')
            return res.status(403).json({ message: 'Forbidden' });
        const { loads } = req.body;
        if (!Array.isArray(loads))
            return res.status(400).json({ message: 'Invalid loads array' });
        const po = await PurchaseOrder_1.PurchaseOrder.findById(poId);
        if (!po)
            return res.status(404).json({ message: 'Purchase order not found' });
        po.loads = loads;
        await po.save();
        res.status(200).json(po);
    }
    catch (err) {
        console.error(err);
        res.status(500).json({ error: 'Failed to update delivery materials' });
    }
});
// DELETE /api/purchase-orders/:id → Delete a purchase order by ID (admin only)
router.delete('/:id', authMiddleware_1.verifyToken, async (req, res) => {
    const user = req.user;
    if (!user || user.role !== 'admin')
        return res.status(403).json({ message: 'Forbidden' });
    try {
        const deleted = await PurchaseOrder_1.PurchaseOrder.findByIdAndDelete(req.params.id);
        if (!deleted)
            return res.status(404).json({ message: 'Purchase order not found' });
        res.json({ message: 'Purchase order deleted' });
    }
    catch (err) {
        res.status(500).json({ message: 'Failed to delete purchase order' });
    }
});
// TODO: Ensure an index exists on PurchaseOrder.createdAt for performance:
// PurchaseOrder.collection.createIndex({ createdAt: -1 });
// POST /api/purchase-orders/:poId/reopen → Reopen a completed PO (ADMIN ONLY)
router.post('/:poId/reopen', authMiddleware_1.verifyToken, async (req, res) => {
    try {
        const { poId } = req.params;
        const user = req.user;
        if (!user || user.role !== 'admin') {
            return res.status(403).json({ message: 'Only admins can reopen POs' });
        }
        const po = await PurchaseOrder_1.PurchaseOrder.findById(poId);
        if (!po) {
            return res.status(404).json({ message: 'Purchase order not found' });
        }
        po.isCompleted = false;
        await po.save();
        res.status(200).json({ message: 'Purchase order reopened successfully', po });
    }
    catch (err) {
        console.error(err);
        res.status(500).json({ error: 'Failed to reopen purchase order' });
    }
});
exports.default = router;
//# sourceMappingURL=PurchaseOrder.js.map