import React, { useState, useEffect } from "react";
import LocationInput from '../components/LocationInput';

const availablePickupLocations: PickupLocation[] = [
  {
    name: "Amsterdam Yard",
    address: "Keizersgracht 123, Amsterdam",
    coordinates: { lat: 52.3676, lng: 4.9041 },
  },
  {
    name: "Rotterdam Dock",
    address: "Coolsingel 1, Rotterdam",
    coordinates: { lat: 51.9225, lng: 4.4792 },
  },
  {
    name: "Utrecht Storage",
    address: "Janskerkhof 10, Utrecht",
    coordinates: { lat: 52.0907, lng: 5.1214 },
  },
];

const availableDeliveryLocations: { name: string; address: string; coordinates: { lat: number; lng: number } }[] = [
  {
    name: "Amsterdam Warehouse",
    address: "Keizersgracht 123, Amsterdam",
    coordinates: { lat: 52.3676, lng: 4.9041 },
  },
  {
    name: "Rotterdam Warehouse",
    address: "Coolsingel 1, Rotterdam",
    coordinates: { lat: 51.9225, lng: 4.4792 },
  },
  {
    name: "Utrecht Warehouse",
    address: "Janskerkhof 10, Utrecht",
    coordinates: { lat: 52.0907, lng: 5.1214 },
  },
  {
    name: "The Hague Depot",
    address: "Binnenhof 1, The Hague",
    coordinates: { lat: 52.0786, lng: 4.2886 },
  },
  {
    name: "Eindhoven Facility",
    address: "Markt 1, Eindhoven",
    coordinates: { lat: 51.4416, lng: 5.4697 },
  },
];

const availableMaterials: string[] = [
  'Fill Dirt',
  'Topsoil',
  'Sand (screened, masonry, concrete)',
  'Gravel (various sizes: #57, #89, crush & run, pea gravel)',
  'Crushed Stone / Crushed Concrete (road base, #57, etc.)',
  'Riprap (various classes)',
  'Lime Rock',
  'Shell Rock / Crushed Shell',
  'Asphalt Millings / Recycled Asphalt',
  'Construction Debris (C&D)',
  'Cleared Vegetation / Tree Stumps',
  'Excavated Earth / Overburden',
  'Concrete Rubble',
  'Asphalt Removal',
];

const availableUnits = [
  { value: 'tons', label: 'Ton' },
  { value: 'cubic_yards', label: 'Cubic Yard (CY)' },
  { value: 'load', label: 'Load' },
  { value: 'each', label: 'Each' },
];

interface Coordinates {
  lat: number;
  lng: number;
}

interface PickupLocation {
  name: string;
  address: string;
  coordinates: Coordinates;
}

interface Material {
  id: number;
  name: string;
  description: string;
}

// LocationData interface for type safety
interface LocationData {
  street: string;
  city: string;
  postalCode: string;
  latitude: number;
  longitude: number;
  state?: string;
}

// Helper to convert a string location to a LocationData object (fallback for legacy data)
const stringToLocationData = (str: string): LocationData => ({
  street: str,
  city: '',
  postalCode: '',
  latitude: 0,
  longitude: 0,
  state: '',
});

// Update Load type to use LocationData
interface Load {
  id: number;
  deliveryLocation: LocationData;
  name?: string;
}

type MaterialEntry = {
  materialId: number;
  materialName: string;
  quantity: number;
  loadType: string;
  rate: number;
  _tempId?: number;
};

interface PoCreateProps {
  onCreated?: () => void;
}

interface Client {
  _id: string;
  name: string;
  deliveryLocations?: {
    name: string;
    address1: string;
    city: string;
    state: string;
    zipCode: string;
  }[];
}

interface Job {
  _id: string;
  clientId: string;
  jobName: string;
  address1: string;
  address2?: string;
  address3?: string;
  city: string;
  state: string;
  zipCode: string;
  lat?: number;
  lng?: number;
  pickupLocations?: { name: string; address1: string; city: string; state: string; zipCode: string; lat?: number; lng?: number }[];
  deliveryLocations?: { name: string; address1: string; city: string; state: string; zipCode: string }[];
  locations?: string[];
}

// Helper to calculate distance between two coordinates (Haversine formula)
function getDistanceKm(lat1: number, lng1: number, lat2: number, lng2: number) {
  const toRad = (v: number) => (v * Math.PI) / 180;
  const R = 6371; // km
  const dLat = toRad(lat2 - lat1);
  const dLng = toRad(lng2 - lng1);
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(toRad(lat1)) * Math.cos(toRad(lat2)) *
    Math.sin(dLng / 2) * Math.sin(dLng / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
}

const PoCreate: React.FC<PoCreateProps> = ({ onCreated }) => {
  // State for client and pickup location
  const [clientName, setClientName] = useState("");
  const [pickupLocation, setPickupLocation] = useState<LocationData>({
    street: '',
    city: '',
    postalCode: '',
    latitude: 0,
    longitude: 0,
    state: '',
  });

  // State for loads (delivery locations)
  const [loads, setLoads] = useState<Load[]>([]);
  // State for materials (attached to pickup)
  const [materials, setMaterials] = useState<MaterialEntry[]>([]);

  // Add state for other charges
  const [otherCharges, setOtherCharges] = useState<{
    description: string;
    quantity: number;
    unit: string;
    rate: number;
    _tempId?: number;
  }[]>([]);

  // Add state for delivery materials per location
  const [deliveryMaterials, setDeliveryMaterials] = useState<{ [loadId: number]: { materialId: number; quantity: number; rate: number }[] }>({});

  // Calculate estimated cost across all materials
  const estimatedCost = Object.values(deliveryMaterials).reduce((sum, mats) =>
    sum + mats.reduce((matSum, m) => matSum + (Number(m.quantity) * Number(m.rate)), 0)
  , 0) +
  otherCharges.reduce((sum, c) => sum + (c.unit === 'lump_sum' ? c.rate : c.quantity * c.rate), 0);

  // Helper: get pickup material by id
  const getPickupMaterial = (materialId: number) => materials.find(m => m.materialId === materialId);

  // Helper: get total assigned for a material across all locations
  const getTotalAssigned = (materialId: number) => Object.values(deliveryMaterials).reduce((sum, mats) => {
    const found = mats.find(m => m.materialId === materialId);
    return sum + (found ? Number(found.quantity) : 0);
  }, 0);

  // Handler for changing delivery material quantity
  const updateDeliveryMaterial = (loadId: number, materialId: number, quantity: number) => {
    setDeliveryMaterials(prev => ({
      ...prev,
      [loadId]: (prev[loadId] || materials.map(m => ({ materialId: m.materialId, quantity: 0, rate: 0 }))).map(mat =>
        mat.materialId === materialId ? { ...mat, quantity } : mat
      )
    }));
  };

  // Handler for changing delivery material rate
  const updateDeliveryMaterialRate = (loadId: number, materialId: number, rate: number) => {
    setDeliveryMaterials(prev => ({
      ...prev,
      [loadId]: (prev[loadId] || materials.map(m => ({ materialId: m.materialId, quantity: 0, rate: 0 }))).map(mat =>
        mat.materialId === materialId ? { ...mat, rate } : mat
      )
    }));
  };

  // Validation: check if any assigned > pickup
  const overAssigned = materials.some(mat => getTotalAssigned(mat.materialId) > mat.quantity);

  // Handler to create PO by sending to backend
  const handleCreatePO = async () => {
    if (!clientName || !pickupLocation.street || loads.length === 0 || materials.length === 0) {
      alert('Please fill in all required fields and add at least one delivery location and one material');
      return;
    }

    // Map LocationData to backend format
    const pickupLocationBackend = {
      name: pickupLocation.street,
      address: [pickupLocation.street, pickupLocation.city, pickupLocation.state, pickupLocation.postalCode].filter(Boolean).join(', '),
      coordinates: {
        lat: pickupLocation.latitude,
        lng: pickupLocation.longitude
      }
    };

    const poData = {
      clientName,
      pickupLocation: pickupLocationBackend,
      materials: materials.map(m => ({
        id: m.materialId,
        name: m.materialName,
        quantity: m.quantity,
        loadType: m.loadType === 'ton' ? 'tons' : m.loadType,
        rate: m.rate
      })),
      loads: loads.map(l => ({
        deliveryLocation: {
          street: l.deliveryLocation.street || '',
          city: l.deliveryLocation.city || '',
          state: l.deliveryLocation.state || '',
          postalCode: l.deliveryLocation.postalCode || '',
          latitude: l.deliveryLocation.latitude || 0,
          longitude: l.deliveryLocation.longitude || 0,
        },
        materials: (deliveryMaterials[l.id] || []).filter(m => m.quantity > 0).map(m => {
          const mat = materials.find(mat => mat.materialId === m.materialId);
          return {
            materialId: m.materialId,
            materialName: mat ? mat.materialName : '',
            quantity: m.quantity,
            loadType: mat ? (mat.loadType === 'ton' ? 'tons' : mat.loadType) : 'tons',
          };
        }),
      })),
    };

    try {
      const response = await fetch('/api/purchase-orders', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(poData),
      });

      if (!response.ok) {
        throw new Error('Failed to create Purchase Order');
      }

      const createdPO = await response.json();
      alert(`Purchase Order created successfully! ID: ${createdPO._id}`);

      // Reset form
      setClientName("");
      setPickupLocation({ street: '', city: '', postalCode: '', latitude: 0, longitude: 0, state: '' });
      setLoads([]);
      setMaterials([]);
      if (onCreated) onCreated();
    } catch (error: any) {
      alert(`Error: ${error.message || 'Unknown error'}`);
    }
  };

  // Update addLoad to use LocationData
  const addLoad = () => {
    const newLoad: Load = {
      id: Date.now(),
      deliveryLocation: {
        street: '', city: '', postalCode: '', latitude: 0, longitude: 0, state: ''
      },
    };
    setLoads([...loads, newLoad]);
  };

  // Restore removeLoad if missing
  const removeLoad = (loadId: number) => {
    setLoads(loads.filter(load => load.id !== loadId));
    setDeliveryLocationIdx(prev => {
      const newIdx = { ...prev };
      delete newIdx[loadId];
      return newIdx;
    });
  };

  // Update updateLoadDeliveryLocation to accept LocationData
  const updateLoadDeliveryLocation = (loadId: number, location: LocationData) => {
    setLoads(loads.map(load =>
      load.id === loadId ? { ...load, deliveryLocation: location } : load
    ));
  };

  // Material management (top-level)
  const addMaterial = () => {
    // Find the first available material not already selected
    const used = new Set(materials.map(m => m.materialName));
    const next = availableMaterials.find(mat => !used.has(mat));
    if (!next) return; // All used
    setMaterials([...materials, {
      materialId: Date.now(),
      materialName: next,
      quantity: 1,
      loadType: 'tons',
      rate: 50,
      _tempId: Date.now() + Math.random()
    }]);
  };
  const removeMaterial = (materialIndex: number) => {
    setMaterials(materials.filter((_, idx) => idx !== materialIndex));
  };
  const updateMaterial = (materialIndex: number, field: string, value: any) => {
    setMaterials(materials => materials.map((mat, idx) => {
      if (idx !== materialIndex) return mat;
      if (field === 'loadType') {
        // Always update rate to default for new loadType
        let newValue = value === 'ton' ? 'tons' : value;
        const newRate = newValue === 'tons' ? 50 : 40;
        return { ...mat, loadType: newValue, rate: newRate };
      }
      return { ...mat, [field]: value };
    }));
  };

  // Add functions for other charges
  const addOtherCharge = () => {
    setOtherCharges([...otherCharges, {
      description: '',
      quantity: 1,
      unit: availableUnits[0].value,
      rate: 0,
      _tempId: Date.now() + Math.random()
    }]);
  };
  const removeOtherCharge = (idx: number) => {
    setOtherCharges(otherCharges.filter((_, i) => i !== idx));
  };
  const updateOtherCharge = (idx: number, field: string, value: any) => {
    setOtherCharges(otherCharges.map((c, i) => i === idx ? { ...c, [field]: value } : c));
  };

  const [clients, setClients] = useState<Client[]>([]);
  const [selectedClient, setSelectedClient] = useState<string>('');
  const [showManualPickup, setShowManualPickup] = useState(false);

  // Add a flag to track if clientName was manually edited
  const [clientNameManuallyEdited, setClientNameManuallyEdited] = useState(false);

  // Add to component state:
  const [pickupLocationIdx, setPickupLocationIdx] = useState<string | number>('');
  const [deliveryLocationIdx, setDeliveryLocationIdx] = useState<{ [loadId: number]: string | number }>({});

  // Add jobs state back
  const [jobs, setJobs] = useState<Job[]>([]);

  useEffect(() => {
    fetch('/api/clients').then(res => res.json()).then(setClients);
  }, []);

  // Fetch jobs for selected client
  useEffect(() => {
    if (!selectedClient) {
      setJobs([]);
      return;
    }
    fetch(`/api/jobs?clientId=${selectedClient}`)
      .then(res => res.json())
      .then(setJobs);
  }, [selectedClient]);

  // Helper to get all unique locations from jobs.locations for the selected client
  const getAllJobLocationsForClient = (): LocationData[] => {
    const locs: LocationData[] = [];
    const seen = new Set();
    jobs.forEach(job => {
      if (Array.isArray(job.locations)) {
        job.locations.filter(loc => typeof loc === 'object' && loc !== null).forEach(loc => {
          const l = loc as { address1: string; city: string; state: string; zipCode: string; lat?: number; lng?: number };
          const key = [l.address1, l.city, l.state, l.zipCode].join('|');
          if (!seen.has(key)) {
            seen.add(key);
            locs.push({
              street: l.address1,
              city: l.city,
              postalCode: l.zipCode,
              latitude: typeof l.lat === 'number' ? l.lat : 0,
              longitude: typeof l.lng === 'number' ? l.lng : 0,
              state: l.state || '',
            });
          }
        });
      }
    });
    return locs;
  };

  // Only show LocationInput for pickup if 'Other (type below)' is selected
  const isPickupValid = (pickupLocationIdx !== '' && pickupLocationIdx !== '__manual__') || (
    pickupLocationIdx === '__manual__' && pickupLocation.street && pickupLocation.city && pickupLocation.state && pickupLocation.postalCode
  );

  return (
    <div className="min-h-screen p-4 md:p-6 max-w-4xl mx-auto font-sans bg-gray-50">
      <div className="bg-white rounded-lg shadow-sm p-4 md:p-6 mb-4">
        <h1 className="text-xl md:text-2xl font-bold mb-4 md:mb-6">Create Purchase Order</h1>

        {/* Client & Job Selection */}
        <div className="mb-4 grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block font-medium mb-1">Client</label>
            <select
              className="w-full p-2 border rounded"
              value={selectedClient}
              onChange={e => {
                setSelectedClient(e.target.value);
                const selected = clients.find(c => c._id === e.target.value);
                if (selected) {
                  if (!clientNameManuallyEdited) setClientName(selected.name);
                } else {
                  setClientName('');
                }
              }}
            >
              <option value="">Select client...</option>
              {clients.map(c => (
                <option key={c._id} value={c._id}>{c.name}</option>
              ))}
            </select>
          </div>
        </div>

        {/* Client Name */}
        <label className="block mb-4">
          <span className="block font-medium mb-1">Client Name</span>
          <input
            type="text"
            value={clientName}
            onChange={e => {
              setClientName(e.target.value);
              setClientNameManuallyEdited(true);
            }}
            placeholder="Enter Client Name"
            className="w-full p-2 border rounded"
          />
        </label>
      </div>

      {/* Job/Location (Pickup) - allow manual override */}
      <div className="bg-white rounded-lg shadow-sm p-4 md:p-6 mb-4">
        <fieldset className="border p-4 rounded">
          <legend className="font-semibold mb-2">Job/Location (Pickup)</legend>
          <label className="block mb-2">
            <span className="block mb-1">Select Job/Location</span>
            <select
              className="w-full p-2 border rounded mb-2"
              value={pickupLocationIdx}
              onChange={e => {
                if (e.target.value === '__manual__') {
                  setPickupLocation({ street: '', city: '', postalCode: '', latitude: 0, longitude: 0, state: '' });
                  setPickupLocationIdx('__manual__');
                } else {
                  const selectedValue = e.target.value;
                  const selectedLoc = getAllJobLocationsForClient().find(loc =>
                    [loc.street, loc.city, loc.state, loc.postalCode].join('|') === selectedValue
                  );
                  if (selectedLoc) {
                    setPickupLocation(selectedLoc);
                    setPickupLocationIdx(selectedValue);
                  }
                }
              }}
            >
              <option value="">Select a location...</option>
              {getAllJobLocationsForClient().map((loc) => {
                const optionValue = [loc.street, loc.city, loc.state, loc.postalCode].join('|');
                // Disable if this location is selected as a delivery location
                const isSelectedAsDelivery = Object.values(deliveryLocationIdx).includes(optionValue);
                return (
                  <option key={optionValue} value={optionValue} disabled={isSelectedAsDelivery}>
                    {loc.street}, {loc.city}, {loc.state} {loc.postalCode}
                  </option>
                );
              })}
              <option value="__manual__">Other (type below)</option>
            </select>
            {/* Only show LocationInput for pickup if 'Other (type below)' is selected */}
            {pickupLocationIdx === '__manual__' && (
              <LocationInput
                location={pickupLocation}
                onChange={setPickupLocation}
                label="Pickup Location"
                placeholder="Search for pickup location..."
              />
            )}
          </label>
        </fieldset>

        {/* Show selection for confirmation */}
        {pickupLocation.street && (
          <div className="text-sm text-gray-600 mt-2">
            <p><strong>Selected:</strong> {pickupLocation.street}</p>
            <p><strong>Lat:</strong> {pickupLocation.latitude}, <strong>Lng:</strong> {pickupLocation.longitude}</p>
          </div>
        )}

        {/* Materials (attached to pickup) */}
        <div className="mt-4">
          <div className="flex justify-between items-center mb-2">
            <span className="font-medium">Pickup Materials</span>
            <button
              onClick={addMaterial}
              className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700"
              disabled={materials.length >= availableMaterials.length}
            >
              Add Material
            </button>
          </div>
          {materials.length === 0 && <p className="text-gray-500 text-sm">No materials added.</p>}
          <div className="max-h-64 overflow-y-auto space-y-2 pr-1">
            {materials.map((material, materialIndex) => (
              <div key={material._tempId || materialIndex} className="flex flex-col md:flex-row flex-wrap gap-2 items-start md:items-center bg-gray-50 rounded p-2">
                <select
                  className="flex-1 min-w-[180px] p-2 border rounded"
                  value={material.materialName}
                  onChange={e => updateMaterial(materialIndex, 'materialName', e.target.value)}
                >
                  {availableMaterials.map((mat) => (
                    <option
                      key={mat}
                      value={mat}
                      disabled={materials.some((m, idx) => idx !== materialIndex && m.materialName === mat)}
                    >
                      {mat}
                    </option>
                  ))}
                </select>
                <input
                  type="number"
                  min="1"
                  placeholder="Qty"
                  className="w-20 p-2 border rounded"
                  value={material.quantity}
                  onChange={e => updateMaterial(materialIndex, 'quantity', Number(e.target.value))}
                />
                <select
                  className="w-32 p-2 border rounded"
                  value={material.loadType}
                  onChange={e => updateMaterial(materialIndex, 'loadType', e.target.value)}
                >
                  {availableUnits.map(u => (
                    <option key={u.value} value={u.value}>{u.label}</option>
                  ))}
                </select>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Job/Location (Delivery) */}
      <div className="bg-white rounded-lg shadow-sm p-4 md:p-6 mb-4">
        <section>
          <div className="flex justify-between items-center mb-2">
            <h2 className="font-semibold">Job/Location (Delivery)</h2>
            <button
              onClick={addLoad}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              Add Job/Location
            </button>
          </div>

          {loads.length === 0 && <p className="text-gray-500">No delivery locations added yet.</p>}

          {loads.map((load, idx) => {
            // Get all selected delivery location values except for this load
            const selectedDeliveryValues = Object.entries(deliveryLocationIdx)
              .filter(([id]) => String(id) !== String(load.id))
              .map(([, val]) => val);
            // Get the pickup value
            const pickupValue = pickupLocationIdx;
            return (
              <div key={load.id} className="mb-4 p-4 border rounded-lg bg-gray-50">
                <div className="flex justify-between items-center mb-2">
                  <h4 className="font-medium">Delivery Location {idx + 1}</h4>
                  <button
                    type="button"
                    className="bg-red-600 text-white text-sm font-semibold px-3 py-1 rounded shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-400"
                    onClick={() => removeLoad(load.id)}
                  >
                    Remove
                  </button>
                </div>
                <select
                  className="w-full p-2 border rounded mb-2"
                  value={deliveryLocationIdx[load.id] || ''}
                  onChange={e => {
                    if (e.target.value === '__manual__') {
                      updateLoadDeliveryLocation(load.id, { street: '', city: '', postalCode: '', latitude: 0, longitude: 0, state: '' });
                      setDeliveryLocationIdx(idx => ({ ...idx, [load.id]: '__manual__' }));
                    } else {
                      const selectedValue = e.target.value;
                      const selectedLoc = getAllJobLocationsForClient().find(loc =>
                        [loc.street, loc.city, loc.state, loc.postalCode].join('|') === selectedValue
                      );
                      if (selectedLoc) {
                        updateLoadDeliveryLocation(load.id, selectedLoc);
                        setDeliveryLocationIdx(i => ({ ...i, [load.id]: selectedValue }));
                      }
                    }
                  }}
                >
                  <option value="">Select a location...</option>
                  {getAllJobLocationsForClient().map((loc) => {
                    const optionValue = [loc.street, loc.city, loc.state, loc.postalCode].join('|');
                    // Disable if this location is selected as pickup or as another delivery location
                    const isSelectedAsPickup = pickupValue === optionValue;
                    const isSelectedElsewhere = selectedDeliveryValues.includes(optionValue);
                    return (
                      <option key={optionValue} value={optionValue} disabled={isSelectedAsPickup || isSelectedElsewhere}>
                        {loc.street}, {loc.city}, {loc.state} {loc.postalCode}
                      </option>
                    );
                  })}
                  <option value="__manual__">Other (type below)</option>
                </select>
                {deliveryLocationIdx[load.id] === '__manual__' && (
                  <LocationInput
                    location={load.deliveryLocation}
                    onChange={loc => updateLoadDeliveryLocation(load.id, loc)}
                    label={`Delivery Location ${idx + 1}`}
                    placeholder="Search for delivery location..."
                  />
                )}
                {/* Per-material quantities */}
                {materials.length > 0 && (
                  <div className="mt-2">
                    <div className="font-semibold mb-1">Material Quantities for this Location (optional)</div>
                    <div className="space-y-2">
                      {materials.map(mat => (
                        <div key={mat.materialId} className="flex flex-col md:flex-row items-start md:items-center gap-2">
                          <span className="w-full md:w-48 truncate text-sm">{mat.materialName} ({availableUnits.find(u => u.value === mat.loadType)?.label || mat.loadType})</span>
                          <input
                            type="number"
                            min="0"
                            max={mat.quantity}
                            className="w-full md:w-24 p-2 border rounded"
                            value={deliveryMaterials[load.id]?.find(m => m.materialId === mat.materialId)?.quantity || ''}
                            onChange={e => updateDeliveryMaterial(load.id, mat.materialId, Number(e.target.value))}
                            placeholder="Qty"
                          />
                          <span className="text-xs text-gray-500">/ {mat.quantity} max</span>
                          <input
                            type="number"
                            min="0"
                            className="w-24 p-2 border rounded"
                            value={deliveryMaterials[load.id]?.find(m => m.materialId === mat.materialId)?.rate || ''}
                            onChange={e => updateDeliveryMaterialRate(load.id, mat.materialId, Number(e.target.value))}
                            placeholder="Rate"
                            title="Rate per unit"
                          />
                          <span className="text-xs text-gray-500">$/unit</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            );
          })}
        </section>
      </div>

      {/* Other Charges Section */}
      <div className="bg-white rounded-lg shadow-sm p-4 md:p-6 mb-4">
        <section>
          <div className="flex justify-between items-center mb-2">
            <h2 className="font-semibold">Other Charges</h2>
            <button
              onClick={addOtherCharge}
              className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
            >
              Add Charge
            </button>
          </div>
          {otherCharges.length === 0 && <p className="text-gray-500">No other charges added yet.</p>}
          {otherCharges.map((charge, idx) => (
            <div key={charge._tempId || idx} className="flex flex-col md:flex-row flex-wrap gap-2 items-start md:items-center bg-gray-50 rounded p-2 mb-2">
              <input
                type="text"
                placeholder="Description"
                className="flex-1 min-w-[120px] p-2 border rounded"
                value={charge.description}
                onChange={e => updateOtherCharge(idx, 'description', e.target.value)}
              />
              <input
                type="number"
                min="1"
                placeholder="Qty"
                className="w-full md:w-20 p-2 border rounded"
                value={charge.quantity}
                onChange={e => updateOtherCharge(idx, 'quantity', Number(e.target.value))}
                disabled={charge.unit === 'lump_sum'}
              />
              <select
                className="w-full md:w-32 p-2 border rounded"
                value={charge.unit}
                onChange={e => updateOtherCharge(idx, 'unit', e.target.value)}
              >
                {availableUnits.map(u => (
                  <option key={u.value} value={u.value}>{u.label}</option>
                ))}
                <option value="lump_sum">Lump Sum</option>
              </select>
              <div className="flex items-center gap-1">
                <input
                  type="number"
                  min="0"
                  className="w-24 p-2 border rounded"
                  value={charge.rate}
                  onChange={e => updateOtherCharge(idx, 'rate', Number(e.target.value))}
                  placeholder="Rate"
                  title="Rate per unit"
                />
                <span className="text-xs text-gray-500">$/unit</span>
              </div>
              <span className="text-sm font-semibold">Total: ${charge.unit === 'lump_sum' ? charge.rate.toFixed(2) : (charge.quantity * charge.rate).toFixed(2)}</span>
              <button
                onClick={() => removeOtherCharge(idx)}
                className="px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
              >
                Remove
              </button>
            </div>
          ))}
        </section>
      </div>

      {/* Purchase Order Estimated Pricing */}
      <div className="bg-white rounded-lg shadow-sm p-4 md:p-6 mb-4">
        <div className="font-semibold text-right text-lg border-t pt-4 mt-4">
          Purchase Order Estimated Pricing: ${estimatedCost.toFixed(2)}
        </div>
      </div>

      {/* Create PO Button */}
      <div className="bg-white rounded-lg shadow-sm p-4 md:p-6">
        <button
          onClick={handleCreatePO}
          className="w-full bg-blue-600 text-white py-3 rounded hover:bg-blue-700"
          disabled={overAssigned}
        >
          Create Purchase Order
        </button>
      </div>
    </div>
  );
};

export default PoCreate;
